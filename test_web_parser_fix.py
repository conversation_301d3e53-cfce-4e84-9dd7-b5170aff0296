#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试网页解析器修复效果的脚本
"""
import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from controller.parser.web_parser import WebParser
from common.logger import logger


async def test_web_parser():
    """测试网页解析器的修复效果"""
    
    # 测试URL列表
    test_urls = [
        {
            "url": "https://www.sohu.com/a/873742663_122354587",
            "title": "搜狐测试页面",
            "content": "这是一个测试内容，用于验证网页解析器的修复效果。"
        },
        {
            "url": "https://www.eet-china.com/mp/a413881.html",
            "title": "EET China测试页面", 
            "content": "另一个测试内容，用于验证网页解析器能否正确处理不同网站。"
        },
        {
            "url": "https://httpbin.org/html",  # 一个可靠的测试URL
            "title": "HTTPBin HTML测试",
            "content": "HTTPBin提供的HTML测试页面，应该能够正常访问。"
        }
    ]
    
    logger.info("开始测试网页解析器修复效果...")
    
    for i, test_case in enumerate(test_urls, 1):
        logger.info(f"\n=== 测试 {i}/{len(test_urls)}: {test_case['title']} ===")
        
        try:
            parser = WebParser(
                url=test_case["url"],
                title=test_case["title"],
                content=test_case["content"]
            )
            
            # 执行解析
            await parser.exec()
            
            # 检查结果
            if parser.chunks:
                logger.info(f"✅ 解析成功: 生成了 {len(parser.chunks)} 个chunks")
                logger.info(f"   第一个chunk内容预览: {parser.chunks[0].plain_content[:100]}...")
            else:
                logger.warning(f"⚠️  解析完成但没有生成chunks")
                
        except Exception as e:
            logger.error(f"❌ 解析失败: {str(e)}")
    
    logger.info("\n测试完成!")


async def test_html_chunker():
    """测试HTML分块器的修复效果"""
    
    logger.info("开始测试HTML分块器修复效果...")
    
    # 包含各种可能导致错误的HTML标签的测试内容
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>测试页面</title>
    </head>
    <body>
        <h1>主标题</h1>
        <p>这是一个段落内容。</p>
        <b>粗体文本</b>
        <em>斜体文本</em>
        <dt>定义术语</dt>
        <dd>定义描述</dd>
        <button>点击按钮</button>
        <option value="1">选项1</option>
        <sub>下标文本</sub>
        <div>
            <span>嵌套内容</span>
        </div>
        <script>console.log('script content');</script>
        <style>body { margin: 0; }</style>
    </body>
    </html>
    """
    
    try:
        from controller.parser.chunker import HtmlChunker
        from controller.parser.chunker.tokenizer import BGE_M3_TOKENIZER
        
        chunker = HtmlChunker(tokenizer=BGE_M3_TOKENIZER)
        chunks = chunker.chunk(html_content=test_html)
        
        logger.info(f"✅ HTML分块成功: 生成了 {len(chunks)} 个chunks")
        for i, chunk in enumerate(chunks):
            logger.info(f"   Chunk {i+1}: {len(chunk.nodes)} 个节点, {chunk.token_counts} tokens")
            logger.info(f"   内容预览: {chunk.plain_content[:100]}...")
            
    except Exception as e:
        logger.error(f"❌ HTML分块失败: {str(e)}")
    
    logger.info("HTML分块器测试完成!")


if __name__ == "__main__":
    async def main():
        await test_html_chunker()
        await test_web_parser()
    
    asyncio.run(main())
