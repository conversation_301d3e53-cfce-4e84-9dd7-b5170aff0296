#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from enum import StrEnum
from datetime import datetime, timedelta

from engine.es import es
from model.doc import get_index


class StatsContent(StrEnum):
    last_docs = "last_docs"  # 最新数据
    doc_count_trend = "doc_count_trend"  # 每日数据量走势
    sentiment_score_range = "sentiment_score_range"  # 情感区间文档数统计
    sentiment_score_trend = "sentiment_score_trend"  # 情感分数每日均值走势
    top_sentiment_docs = "top_sentiment_docs"  # 情绪最高和最低的文档
    top_sentiment_tags = "top_sentiment_tags"  # 情绪最高和最低的标签
    top_subject = "top_subject"  # 出现最多的实体
    top_doc_keywords = "top_doc_keywords"  # 出现最多的关键词
    top_view_keywords = "top_view_keywords"  # 出现最多的正面和负面观点关键词
    view_count_ratio_trend = "view_count_ratio_trend"  # 每日正面和负面观点比例走势


class StatsController:
    async def get_doc_stats(self, stats_contents: list[StatsContent], keywords: list[str] = None,
                            exclude_keywords: list[str] = None, repo_ids: list[int] = None, start: str = None,
                            end: str = None, size: int = 0):
        # 时间补全
        index = get_index(repo_ids=repo_ids)
        if not start or not end:
            data_start, data_end = await self.get_date_range_from_index(index=index)
            if not start:
                start = data_start
            if not end:
                end = data_end

        aggs = {}
        process_funcs = []
        if StatsContent.last_docs in stats_contents:
            # 仅有func
            process_funcs.append(self.last_docs())
            size = 5

        if StatsContent.doc_count_trend in stats_contents:
            condition, process_func = await self.doc_count_trend(start=start, end=end)
            aggs.update(condition)
            process_funcs.append(process_func)

        if StatsContent.sentiment_score_range in stats_contents:
            condition, process_func = self.sentiment_score_range()
            aggs.update(condition)
            process_funcs.append(process_func)

        if StatsContent.sentiment_score_trend in stats_contents:
            condition, process_func = await self.sentiment_score_trend(start=start, end=end)
            aggs.update(condition)
            process_funcs.append(process_func)

        if StatsContent.top_sentiment_docs in stats_contents:
            condition, process_func = self.top_sentiment_docs()
            aggs.update(condition)
            process_funcs.append(process_func)

        if StatsContent.top_sentiment_tags in stats_contents:
            condition, process_func = self.top_sentiment_tags()
            aggs.update(condition)
            process_funcs.append(process_func)

        if StatsContent.top_subject in stats_contents:
            condition, process_func = self.top_subject()
            aggs.update(condition)
            process_funcs.append(process_func)

        if StatsContent.top_doc_keywords in stats_contents:
            condition, process_func = self.top_doc_keywords()
            aggs.update(condition)
            process_funcs.append(process_func)

        if StatsContent.top_view_keywords in stats_contents:
            condition, process_func = self.top_view_keywords()
            aggs.update(condition)
            process_funcs.append(process_func)

        if StatsContent.view_count_ratio_trend in stats_contents:
            condition, process_func = await self.top_view_ratio_trend(start=start, end=end)
            aggs.update(condition)
            process_funcs.append(process_func)

        # 构建查询
        filters = [{
            "range": {
                "data_time": {
                    "gte": start,
                    "lte": end
                }
            }
        }]

        should = []
        must_nots = []
        if keywords:
            for kw in keywords:
                # 对text字段使用phrase查询，对keyword字段使用普通multi_match
                should.extend([
                    {
                        "multi_match": {
                            "query": kw,
                            "type": "phrase",
                            "fields": ["plain_text", "filename"],
                            "analyzer": "ik_max_word"
                        }
                    },
                    {
                        "multi_match": {
                            "query": kw,
                            "fields": ["author", "source", "tags"]
                        }
                    }
                ])

        if exclude_keywords:
            for kw in exclude_keywords:
                # 对text字段使用phrase查询，对keyword字段使用普通multi_match
                must_nots.extend([
                    {
                        "multi_match": {
                            "query": kw,
                            "type": "phrase",
                            "fields": ["plain_text", "filename"],
                            "analyzer": "ik_max_word"
                        }
                    },
                    {
                        "multi_match": {
                            "query": kw,
                            "fields": ["author", "source", "tags"]
                        }
                    }
                ]
            )

        bool_query = {
            "bool": {
                "filter": filters,
                "must_not": must_nots,
            }
        }
        if should:
            bool_query["bool"]["should"] = should
            bool_query["bool"]["minimum_should_match"] = 1

        res = await es.search(
            index=index,
            query=bool_query,
            aggs=aggs,
            size=size,
            track_total_hits=True,
            source_includes=["title", "data_time", "doc_id"],
            sort=[{"data_time": "desc"}])

        return_stats = {
            "total": res["hits"]["total"]["value"],
        }
        for func in process_funcs:
            return_stats.update(func(res))

        return return_stats

    @staticmethod
    async def get_date_range_from_index(index: str):
        """获取索引中最老和最新的数据时间"""
        res = await es.search(
            index=index,
            query={"match_all": {}},
            aggs={
                "min_date": {
                    "min": {
                        "field": "data_time"
                    }
                },
                "max_date": {
                    "max": {
                        "field": "data_time"
                    }
                }
            },
            size=0
        )
        min_date = None
        max_date = None
        if res["aggregations"]["min_date"]["value"]:
            # 有min就有max
            min_date = res["aggregations"]["min_date"]["value_as_string"]
            max_date = res["aggregations"]["max_date"]["value_as_string"]

        return min_date, max_date

    @staticmethod
    def last_docs():
        return lambda res: {"last_docs": [{"doc_id": hit["_source"]} for hit in res["hits"]["hits"]]}

    @staticmethod
    async def doc_count_trend(start: str, end: str):
        """每日数据量走势"""
        # 确保时间格式为日期格式
        start_date = datetime.strptime(start[:10], "%Y-%m-%d")
        end_date = datetime.strptime(end[:10], "%Y-%m-%d")
        start = start_date.strftime("%Y-%m-%d")
        end = end_date.strftime("%Y-%m-%d")

        condition = {
            "doc_count_trend": {
                "date_histogram": {
                    "field": "data_time",
                    "calendar_interval": "1d",
                    "format": "yyyy-MM-dd",
                    "min_doc_count": 0,
                    "extended_bounds": {
                        "min": start,
                        "max": end
                    }
                }
            }
        }

        def process_func(res):
            # 生成完整的日期范围
            current_date = start_date
            date_range = []
            while current_date <= end_date:
                date_range.append(current_date.strftime("%Y-%m-%d"))
                current_date += timedelta(days=1)

            # 从ES结果中提取数据
            buckets = res["aggregations"]["doc_count_trend"]["buckets"]
            data_dict = {}
            for bucket in buckets:
                date_key = bucket["key_as_string"]
                doc_count = bucket["doc_count"]
                data_dict[date_key] = doc_count

            # 填充缺失日期为0
            result = []
            for date_str in date_range:
                result.append({
                    "date": date_str,
                    "value": data_dict.get(date_str, 0)
                })

            return {"doc_count_trend": result}

        return condition, process_func

    @staticmethod
    def sentiment_score_range(min_positive: int = 35, max_negative: int = -35):
        """情感区间文档数"""
        condition =  {
            # 情感区间文档数
            "sentiment_score_range": {
                "range": {
                    "field": "extract_result.sentiment_score",
                    "ranges": [
                        {"key": "正面", "from": -100, "to": max_negative + 1},  # [0, <-34]
                        {"key": "中性", "from": max_negative + 1, "to": min_positive},  # [-35, 35]
                        {"key": "负面", "from": min_positive, "to": 101}  # [36, <101]
                    ],
                    "keyed": True
                }
            },
        }
        process_func = lambda res: {
            "sentiment_score_range": [
                {"name": key, "value": bucket["doc_count"]}
                for key, bucket in res["aggregations"]["sentiment_score_range"]["buckets"].items()]}

        return condition, process_func

    @staticmethod
    async def sentiment_score_trend(start: str | None, end: str | None):
        """情感分数每日均值走势"""
        # 确保时间格式为日期格式
        if start:
            start_date = datetime.strptime(start[:10], "%Y-%m-%d")
            start = start_date.strftime("%Y-%m-%d")
        if end:
            end_date = datetime.strptime(end[:10], "%Y-%m-%d")
            end = end_date.strftime("%Y-%m-%d")

        condition = {
            "sentiment_score_trend": {
                "date_histogram": {
                    "field": "data_time",
                    "calendar_interval": "1d",
                    "format": "yyyy-MM-dd",
                    "min_doc_count": 0,
                    "extended_bounds": {
                        "min": start,
                        "max": end
                    }
                },
                "aggs": {
                    "avg_sentiment": {
                        "avg": {
                            "field": "extract_result.sentiment_score"
                        }
                    }
                }
            }
        }

        def process_func(res):
            # 生成完整的日期范围
            if not start and not end:
                return {"sentiment_score_trend": []}
            current_date = start_date
            date_range = []
            while current_date <= end_date:
                date_range.append(current_date.strftime("%Y-%m-%d"))
                current_date += timedelta(days=1)

            # 从ES结果中提取数据
            buckets = res["aggregations"]["sentiment_score_trend"]["buckets"]
            data_dict = {}
            for bucket in buckets:
                date_key = bucket["key_as_string"]
                avg_value = bucket["avg_sentiment"]["value"]
                data_dict[date_key] = round(avg_value, 2) if avg_value is not None else 0

            # 填充缺失日期为0
            result = []
            for date_str in date_range:
                result.append({
                    "date": date_str,
                    "value": data_dict.get(date_str, 0)
                })

            return {"sentiment_score_trend": result}

        return condition, process_func

    @staticmethod
    def top_sentiment_docs():
        """情绪最高和最低的文档"""
        condition = {
            # 情绪最高的5条文档
            "top_positive_docs": {
                "terms": {
                    "field": "extract_result.sentiment_score",
                    "size": 5,
                    "order": {"_key": "desc"}
                },
                "aggs": {
                    "top_docs": {
                        "top_hits": {
                            "size": 5,
                            "_source": ["title", "extract_result.sentiment_score", "data_time"]
                        }
                    }
                }
            },
            # 情绪最低的5条文档
            "top_negative_docs": {
                "terms": {
                    "field": "extract_result.sentiment_score",
                    "size": 5,
                    "order": {"_key": "asc"}
                },
                "aggs": {
                    "top_docs": {
                        "top_hits": {
                            "size": 5,
                            "_source": ["title", "extract_result.sentiment_score", "data_time"]
                        }
                    }
                }
            }
        }
        process_func = lambda res: {
            "top_positive_docs": [
                {"doc_id": hit["_id"], **hit["_source"]}
                for bucket in res["aggregations"]["top_positive_docs"]["buckets"]
                for hit in bucket["top_docs"]["hits"]["hits"]
            ][:5],
            "top_negative_docs": [
                {"doc_id": hit["_id"], **hit["_source"]}
                for bucket in res["aggregations"]["top_negative_docs"]["buckets"]
                for hit in bucket["top_docs"]["hits"]["hits"]
            ][:5]
        }
        return condition, process_func

    @staticmethod
    def top_sentiment_tags():
        """情绪最高和最低的标签"""
        condition = {
            # 情绪最高的标签
            "top_positive_tags": {
                "terms": {
                    "field": "tags",
                    "size": 1,
                    "order": {
                        "avg_sentiment": "desc"
                    }
                },
                "aggs": {
                    "avg_sentiment": {
                        "avg": {
                            "field": "extract_result.sentiment_score"
                        }
                    }
                }
            },
            # 情绪最低的标签
            "top_negative_tags": {
                "terms": {
                    "field": "tags",
                    "size": 1,
                    "order": {
                        "avg_sentiment": "asc"
                    }
                },
                "aggs": {
                    "avg_sentiment": {
                        "avg": {
                            "field": "extract_result.sentiment_score"
                        }
                    }
                }
            }
        }
        process_func = lambda res: {
            "top_positive_tags": [
                {"name": bucket["key"], "value": bucket["avg_sentiment"]["value"]}
                for bucket in res["aggregations"]["top_positive_tags"]["buckets"]
            ],
            "top_negative_tags": [
                {"name": bucket["key"], "value": bucket["avg_sentiment"]["value"]}
                for bucket in res["aggregations"]["top_negative_tags"]["buckets"]
            ]
        }
        return condition, process_func

    @staticmethod
    def top_subject():
        """出现最多的实体"""
        condition = {
            "top_subject": {
                "terms": {
                    "field": "extract_result.subject.keyword",
                    "size": 5
                }
            }
        }
        process_func = lambda res: {
            "top_subject": [
                {"name": bucket["key"], "value": bucket["doc_count"]}
                for bucket in res["aggregations"]["top_subject"]["buckets"]
            ]
        }
        return condition, process_func

    @staticmethod
    def top_doc_keywords(size: int = 50):
        """出现最多的关键词"""
        condition = {
            "top_doc_keywords": {
                "terms": {
                    "field": "keywords",
                    "size": size
                }
            }
        }
        process_func = lambda res: {
            "top_doc_keywords": [
                {"name": bucket["key"], "value": bucket["doc_count"]}
                for bucket in res["aggregations"]["top_doc_keywords"]["buckets"]
            ]
        }
        return condition, process_func

    @staticmethod
    def top_view_keywords(positive_size: int = 50, negative_size: int = 50):
        """出现最多的正面和负面观点关键词"""
        condition = {
            # 出现最多的正面观点关键词
            "top_positive_view_keywords": {
                "terms": {
                    "field": "extract_result.positive_view_keywords",
                    "size": positive_size
                }
            },
            # 出现最多的负面观点关键词
            "top_negative_view_keywords": {
                "terms": {
                    "field": "extract_result.negative_view_keywords",
                    "size": negative_size
                }
            }
        }
        process_func = lambda res: {
            "top_positive_view_keywords": [
                {"name": bucket["key"], "value": bucket["doc_count"]}
                for bucket in res["aggregations"]["top_positive_view_keywords"]["buckets"]
            ],
            "top_negative_view_keywords": [
                {"name": bucket["key"], "value": bucket["doc_count"]}
                for bucket in res["aggregations"]["top_negative_view_keywords"]["buckets"]
            ]
        }
        return condition, process_func

    @staticmethod
    async def top_view_ratio_trend(start: str, end: str):
        """每日正面和负面观点比例走势"""
        # 确保时间格式为日期格式
        start_date = datetime.strptime(start[:10], "%Y-%m-%d")
        end_date = datetime.strptime(end[:10], "%Y-%m-%d")
        start = start_date.strftime("%Y-%m-%d")
        end = end_date.strftime("%Y-%m-%d")

        condition = {
            "top_view_ratio_trend": {
                "date_histogram": {
                    "field": "data_time",
                    "calendar_interval": "1d",
                    "format": "yyyy-MM-dd",
                    "min_doc_count": 0,
                    "extended_bounds": {
                        "min": start,
                        "max": end
                    }
                },
                "aggs": {
                    "positive_view_count": {
                        "sum": {
                            "script": {
                                "source": "if (params._source.extract_result != null && params._source.extract_result.positive_view != null) { return params._source.extract_result.positive_view.size(); } return 0;"
                            }
                        }
                    },
                    "negative_view_count": {
                        "sum": {
                            "script": {
                                "source": "if (params._source.extract_result != null && params._source.extract_result.negative_view != null) { return params._source.extract_result.negative_view.size(); } return 0;"
                            }
                        }
                    }
                }
            }
        }

        def process_func(res):
            # 生成完整的日期范围
            current_date = start_date
            date_range = []
            while current_date <= end_date:
                date_range.append(current_date.strftime("%Y-%m-%d"))
                current_date += timedelta(days=1)

            # 从ES结果中提取数据
            buckets = res["aggregations"]["top_view_ratio_trend"]["buckets"]
            data_dict = {}
            for bucket in buckets:
                date_key = bucket["key_as_string"]
                doc_count = bucket["doc_count"]
                positive_count = bucket["positive_view_count"]["value"]
                negative_count = bucket["negative_view_count"]["value"]

                # 计算比例
                positive_ratio = round(positive_count / doc_count, 4) if doc_count > 0 else 0
                negative_ratio = round(negative_count / doc_count, 4) if doc_count > 0 else 0

                data_dict[date_key] = {
                    "positive_ratio": positive_ratio,
                    "negative_ratio": negative_ratio,
                    "positive_count": int(positive_count),
                    "negative_count": int(negative_count),
                    "total_count": doc_count
                }

            # 填充缺失日期为0
            result = []
            for date_str in date_range:
                if date_str in data_dict:
                    result.append({
                        "date": date_str,
                        **data_dict[date_str]
                    })
                else:
                    result.append({
                        "date": date_str,
                        "positive_ratio": 0,
                        "negative_ratio": 0,
                        "positive_count": 0,
                        "negative_count": 0,
                        "total_count": 0
                    })
            top_positive_view_ratio_trend = [{"name": r["date"], "value": r["positive_ratio"]} for r in result]
            top_negative_view_ratio_trend = [{"name": r["date"], "value": r["negative_ratio"]} for r in result]
            return {
                "positive_view_count_ratio_trend": top_positive_view_ratio_trend,
                "negative_view_count_ratio_trend": top_negative_view_ratio_trend
            }

        return condition, process_func



Stats = StatsController()
