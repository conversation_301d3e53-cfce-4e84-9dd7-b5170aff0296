#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import re
import datetime

from sqlalchemy import select, update, func, cast, JSON

from engine.rdb import g, paginator, query_order, fetch_one, fetch_all, fetch_all_sync
from engine.es import es_sync, es
from model.summarizer import SummarizerTaskModel, SummarizerTaskState
from model.summarizer_task_es import SUMMARIZER_TASK_INDEX, SummarizerTaskDoc
from common.time import now_datetime_str
from common.logger import logger
from controller.repository import Doc


class SummarizerTaskController:
    extract_citation_pattern = re.compile(r"\[citation:(\d+)]")
    extract_numbers_pattern = re.compile(r"\[(\d+)\]\(\)")

    @staticmethod
    def get_query(summarizer_task_id: int = None, summarizer_id: int = None, repo_id: int = None, match: str = None,
                  state: int = None, start: str = None, end: str = None, order_by: str = None):
        columns = [
            SummarizerTaskModel.id.label("summarizer_task_id"),
            SummarizerTaskModel.summarizer_name,
            SummarizerTaskModel.repo_ids,
            SummarizerTaskModel.summarizer_id,
            SummarizerTaskModel.summarizer_config,
            SummarizerTaskModel.update_time
        ]

        where = [SummarizerTaskModel.is_delete == 0]
        if summarizer_task_id is not None:
            where.append(SummarizerTaskModel.id == summarizer_task_id)
        if summarizer_id is not None:
            where.append(SummarizerTaskModel.summarizer_id == summarizer_id)
        if repo_id is not None:
            where.append(func.json_contains(SummarizerTaskModel.repo_ids, cast(repo_id, JSON)))
        if match:
            where.append(SummarizerTaskModel.summarizer_name.ilike(f"%{match}%"))
        if state is not None:
            where.append(SummarizerTaskModel.state == state)
        if start is not None:
            where.append(SummarizerTaskModel.update_time >= start)
        if end is not None:
            where.append(SummarizerTaskModel.update_time < end)

        query = select(*columns).where(*where)

        query = query_order(query=query, order_by=order_by, table=SummarizerTaskModel)
        return query

    async def get_source(self, summarizer_task_id: int):
        source = await es.get_source(
            index=SUMMARIZER_TASK_INDEX,
            id=summarizer_task_id,
            source_includes=["markdown", "related_doc_ids", "input_tokens", "output_tokens"])

        related_doc_ids = source.pop("related_doc_ids")
        markdown = source.pop("markdown")

        citation_idx = self.extract_citation_pattern.findall(markdown)

        citation_mapping = {}
        doc_ids = []
        index = 0
        for cid in citation_idx:
            cid = int(cid)
            doc_id = related_doc_ids[cid]
            doc_ids.append(doc_id)
            if cid not in citation_mapping:
                citation_mapping[cid] = {
                    "index": index+1,
                    "doc_id": doc_id
                }
                index += 1

        docs = await Doc.get_es_all(doc_ids=doc_ids, includes=["title", "doc_id"])
        docs_mapping = {doc["doc_id"]: doc for doc in docs}

        related_docs = []
        for cid, record in citation_mapping.items():
            markdown = markdown.replace(f"[citation:{cid}]", f"[{record['index']}]()")
            record["title"] = docs_mapping[record["doc_id"]]["title"]
            related_docs.append(record)

        # 将连续的[int]()合并为[int1,int2,...]()
        def replace_match(match):
            numbers = self.extract_numbers_pattern.findall(match.group())
            return f"[{','.join(numbers)}]()"
        markdown = re.sub(r"(\[\d+]\(\))+", replace_match, markdown)

        return {
            "markdown": markdown,
            "related_docs": related_docs,
            "input_tokens": source.get("input_tokens"),
            "output_tokens": source.get("output_tokens"),
        }

    @staticmethod
    def get_es_query(summarizer_task_id: int = None, summarizer_task_ids: list[int] = None, summarizer_id: int = None,
                     start: str = None, end: str = None):
        filters = []
        if summarizer_task_id is not None:
            filters.append({"term": {"summarizer_task_id": summarizer_task_id}})
        if summarizer_task_ids is not None:
            filters.append({"terms": {"summarizer_task_id": summarizer_task_ids}})
        if summarizer_id is not None:
            filters.append({"term": {"summarizer_id": summarizer_id}})
        if (start is not None) or (end is not None):
            range_time = {}
            if start:
                range_time["gte"] = start
            if end:
                range_time["lt"] = end
            filters.append({"range": {"data_time": range_time}})

        query = {
            "bool": {
                "filter": filters
            }
        }

        return query

    @staticmethod
    def get_last_tasks_query(summarizer_ids: list[int]):
        query = (
            select(
                func.max(SummarizerTaskModel.id).label("summarizer_task_id"))
            .where(SummarizerTaskModel.id.in_(summarizer_ids),
                   SummarizerTaskModel.is_delete == 0,
                   SummarizerTaskModel.state == SummarizerTaskState.succeeded)
            .group_by(SummarizerTaskModel.summarizer_id))

        return query

    async def get_list(self, repo_id: int = None, summarizer_id: int = None, match: str = None, state: int = None,
                       page: int = 1, per_page: int = 20, start: str = None, end: str = None, order_by: str = None):
        query = self.get_query(
            repo_id=repo_id, summarizer_id=summarizer_id, match=match, state=state, start=start, end=end,
            order_by=order_by)
        return await paginator(query, page=page, per_page=per_page)

    async def get_all(self, repo_id: int = None, summarizer_id: int = None, match: str = None, state: int = None,
                      order_by: str = None):
        query = self.get_query(
            repo_id=repo_id, summarizer_id=summarizer_id, match=match, state=state, order_by=order_by)
        return await fetch_all(query)

    async def get_one(self, summarizer_task_id: int):
        query = self.get_query(summarizer_task_id=summarizer_task_id)
        return await fetch_one(query)

    async def get_task_tokens_mapping(self, summarizer_task_ids: list[int]):
        query = self.get_es_query(summarizer_task_ids=summarizer_task_ids)
        res = await es.search(
            index=SUMMARIZER_TASK_INDEX,
            query=query,
            size=10000,
            source_includes=["summarizer_task_id", "input_tokens", "output_tokens"])
        return {hit["_source"].pop("summarizer_task_id"): hit["_source"] for hit in res["hits"]["hits"]}

    async def get_summary_tokens(self, start: str, end: str):
        query = self.get_es_query( start=start, end=end)
        res = await es.search(
            index=SUMMARIZER_TASK_INDEX,
            query=query,
            aggs={
                "sum_input_tokens": {
                    "sum": {
                        "field": "input_tokens"
                    }
                },
                "sum_output_tokens": {
                    "sum": {
                        "field": "output_tokens"
                    }
                }
            },
            size=0)

        return {
            "input_tokens": int(res["aggregations"]["sum_input_tokens"]["value"]),
            "output_tokens": int(res["aggregations"]["sum_output_tokens"]["value"])
        }

    def get_last_tasks_sync(self, summarizer_ids: list[int]):
        query = self.get_last_tasks_query(summarizer_ids=summarizer_ids)
        return fetch_all_sync(query)

    @staticmethod
    async def create(summarizer_id: int, summarizer_name: str, repo_ids: list[int], summarizer_config: dict,
               related_doc_ids: list[int], create_time: datetime.datetime):
        summarizer_config = json.loads(json.dumps(summarizer_config, ensure_ascii=False, default=str))
        task = SummarizerTaskModel(
            summarizer_id=summarizer_id, summarizer_name=summarizer_name, repo_ids=repo_ids,
            summarizer_config=summarizer_config, related_doc_ids=related_doc_ids, state=SummarizerTaskState.running, create_time=create_time)
        g.session.add(task)
        await g.session.flush()

        return task.id

    async def get_task_result(self, summarizer_id: int):
        query = self.get_es_query(summarizer_id=summarizer_id)
        res = await es.search(
            index=SUMMARIZER_TASK_INDEX,
            query=query,
            sort=[{"summarizer_task_id": "asc"}],
            size=1,
            source_includes=["markdown"])

        hits = res["hits"]["hits"]
        if hits:
            return {"_id": hits[0]["_id"], **hits[0]["_source"]}
        return None

    @staticmethod
    async def update(summarizer_task_id: int, state: int = None, message: str = None):
        update_info = {}
        if state is not None:
            update_info[SummarizerTaskModel.state] = state

        if message is not None:
            update_info[SummarizerTaskModel.message] = message

        query = update(SummarizerTaskModel) \
            .where(SummarizerTaskModel.id == summarizer_task_id) \
            .values(update_info)

        result = await g.session.execute(query)
        await g.session.flush()
        return result.rowcount

    @staticmethod
    async def create_es(_id: str, summarizer_task_id: int, summarizer_id: int, related_doc_ids: list[str | int],
                        model: str | int, data_time: str = None, markdown: str = None, input_tokens: int = None,
                        output_tokens: int = None):
        now_time_str = now_datetime_str()
        if data_time is None:
            data_time = now_time_str

        source = SummarizerTaskDoc(
            summarizer_task_id=summarizer_task_id, summarizer_id=summarizer_id, related_doc_ids=related_doc_ids,
            model=model, data_time=data_time, markdown=markdown, input_tokens=input_tokens, output_tokens=output_tokens,
            create_time=now_time_str).model_dump()

        res = await es.index(
            index=SUMMARIZER_TASK_INDEX,
            id=_id,
            document=source)

        return res

    @staticmethod
    async def update_es(_id: str, markdown: str = None, input_tokens: int = None, output_tokens: int = None):
        doc = {
            "markdown": markdown,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens
        }
        res = await es.update(
            index=SUMMARIZER_TASK_INDEX,
            id=_id,
            body={
                "doc": doc
            }
        )
        return res


SummarizerTask = SummarizerTaskController()
