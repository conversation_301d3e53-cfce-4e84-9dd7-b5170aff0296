#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from enum import Enum
from itertools import product, chain
from typing import Optional

from sqlalchemy import select, update, func
from pydantic import BaseModel, model_validator, Field
from taskiq.scheduler.scheduled_task import CronSpec

from config import TZ
from common.logger import logger
from engine.rdb import g, query_order, fetch_one, fetch_all, fetch_all_sync, paginator
from engine.taskiq_task import broker, redis_schedule_source
from model.analytics import AnalyticsDashboardModel, AnalyticsItemModel, AnalyticsMetricModel
from exception.custom_exception import ParamsCheckError
from controller.analytics import Summarizer


class MetricType(str, Enum):
    int = "int"
    str = "str"


class Metric(BaseModel):
    metric_id: Optional[int] = Field(None, title="量化指标ID")
    name: str = Field(title="量化指标名称", min_length=1, max_length=12)
    type_: MetricType = Field(title="量化指标类型")
    min_: int = Field(title="量化指标最小值/最小长度", ge=0)
    max_: int = Field(title="量化指标最大值/最大长度", ge=0)
    require_: str = Field(title="量化指标分析要求")

    @model_validator(mode="after")
    def check_total(self):
        if self.min_ > self.max_:
            raise ValueError(f"量化指标[{self.name}] 最小值设置大于最大值")
        return self


class AnalyticsDashboardController:
    @staticmethod
    def check(combin_summarizers: list[list[int]], sup_summarizers: list[list[int]] = None):
        if combin_summarizers is None:
            return
        set_combin_summarizer_ids = set(chain.from_iterable(combin_summarizers))
        for combin_layer in combin_summarizers:
            if not combin_layer:
                raise ParamsCheckError(f"组合摘要分析有空的层级,请检查数据")
        if sum([len(s) for s in combin_summarizers]) != len(set_combin_summarizer_ids):
            raise ParamsCheckError(f"组合摘要分析有重复id,请检查数据")

        if sup_summarizers:
            for sup_layer in sup_summarizers:
                if not sup_layer:
                    raise ParamsCheckError(f"补充摘要分析有空的层级,请检查数据")
            unique_sup_summarizer_ids = set(chain.from_iterable(sup_summarizers))
            if sum([len(s) for s in sup_summarizers]) != len(unique_sup_summarizer_ids):
                raise ParamsCheckError(f"补充摘要分析有重复id,请检查数据")
            for sup_summary_id in chain.from_iterable(sup_summarizers):
                if sup_summary_id in set_combin_summarizer_ids:
                    raise ParamsCheckError(f"补充摘要分析与组合摘要分析有重复id,请检查数据")

    @staticmethod
    def get_query(dashboard_id: int = None, match: str = None, order_by: str = None):
        where = [AnalyticsDashboardModel.is_delete == 0]
        if dashboard_id is not None:
            where.append(AnalyticsDashboardModel.id == dashboard_id)
        if match:
            where.append(AnalyticsDashboardModel.name.ilike(f"%{match}%"))

        item_subquery = (
            select(
                AnalyticsItemModel.dashboard_id,
                func.count(AnalyticsItemModel.id).label("item_count"),
                func.max(AnalyticsItemModel.update_time).label("last_update_time"))
            .where(AnalyticsItemModel.is_delete == 0)
            .group_by(AnalyticsItemModel.dashboard_id)
            .subquery()
        )

        columns = [
            AnalyticsDashboardModel.id.label("dashboard_id"),
            AnalyticsDashboardModel.name,
            AnalyticsDashboardModel.create_time,
            AnalyticsDashboardModel.update_time,
            func.coalesce(item_subquery.c.item_count, 0).label("item_count"),
            item_subquery.c.last_update_time,
        ]
        if dashboard_id is not None:
            # 详情查询
            columns.extend([
                AnalyticsDashboardModel.execution,
                AnalyticsDashboardModel.combin_summarizer_ids,
                AnalyticsDashboardModel.sup_summarizer_ids,
                AnalyticsDashboardModel.focus,
                AnalyticsDashboardModel.add_info,
            ])

        query = (
            select(*columns)
            .join(item_subquery, AnalyticsDashboardModel.id == item_subquery.c.dashboard_id, isouter=True)
            .where(*where))

        query = query_order(query=query, table=AnalyticsDashboardModel, order_by=order_by)
        return query

    @staticmethod
    def get_metric_query(dashboard_id: int):
        query = (
            select(
                AnalyticsMetricModel.id.label("metric_id"),
                AnalyticsMetricModel.name,
                AnalyticsMetricModel.type_,
                AnalyticsMetricModel.min_,
                AnalyticsMetricModel.max_,
                AnalyticsMetricModel.require_)
            .where(
                AnalyticsMetricModel.dashboard_id == dashboard_id,
                AnalyticsMetricModel.is_delete == 0)
            .order_by(AnalyticsMetricModel.id))

        return query

    async def get_one(self, dashboard_id: int, match: str = None, order_by: str = None):
        query = self.get_query(dashboard_id=dashboard_id, match=match, order_by=order_by)
        dashboard = await fetch_one(query)

        metrics = await self.get_metric_all(dashboard_id=dashboard_id)
        dashboard["metrics"] = metrics

        summarizer_ids = set(chain.from_iterable(dashboard["combin_summarizer_ids"]))
        if dashboard["sup_summarizer_ids"]:
            summarizer_ids += chain.from_iterable(dashboard["sup_summarizer_ids"])
        summarizer_ids = list(summarizer_ids)
        summarizers = await Summarizer.get_all(summarizer_ids=summarizer_ids, is_delete=None)
        summarizer_id_mapping = {s["summarizer_id"]: s for s in summarizers}

        keep_summarizer_keys = ("summarizer_id", "name", "tags", "is_delete")

        dashboard["combin_summarizers"] = [
            [{
                k: v for k, v in summarizer_id_mapping[sid].items() if k in keep_summarizer_keys
            } for sid in layer]
            for layer in dashboard["combin_summarizer_ids"]]
        dashboard["sup_summarizers"] = [
            [{
                k: v for k, v in summarizer_id_mapping[sid].items() if k in keep_summarizer_keys
            } for sid in layer]
            for layer in dashboard["sup_summarizer_ids"]] if dashboard["sup_summarizer_ids"] else []

        return dashboard

    async def get_list(self, match: str, page: int = 1, per_page: int = 20, order_by: str = None):
        query = self.get_query(match=match, order_by=order_by)
        return await paginator(query=query, page=page, per_page=per_page)

    async def get_metric_all(self, dashboard_id: int):
        query = self.get_metric_query(dashboard_id=dashboard_id)
        return await fetch_all(query)

    def get_metric_all_sync(self, dashboard_id: int):
        query = self.get_metric_query(dashboard_id=dashboard_id)
        return fetch_all_sync(query)

    @staticmethod
    def get_item_query(dashboard_id: int = None, item_id: int = None):
        where = [AnalyticsItemModel.is_delete == 0]
        if dashboard_id:
            where.append(AnalyticsItemModel.dashboard_id == dashboard_id)
        if item_id is not None:
            where.append(AnalyticsItemModel.id == item_id)

        query = (
            select(
                AnalyticsItemModel.id.label("item_id"),
                AnalyticsItemModel.name,
                AnalyticsItemModel.combin_summarizer_ids,
                AnalyticsItemModel.sup_summarizer_ids,
                AnalyticsItemModel.update_time)
            .where(*where)
            .order_by(AnalyticsItemModel.id))

        return query

    async def get_item_all(self, dashboard_id: int = None, item_id: int = None):
        query = self.get_item_query(dashboard_id=dashboard_id, item_id=item_id)
        return await fetch_all(query)

    async def get_item_one(self, item_id: int):
        query = self.get_item_query(item_id=item_id)
        return await fetch_one(query)

    @staticmethod
    async def create(name: str, execution: str, focus: str, combin_summarizers: list[list[int]], metrics: list[dict],
                     sup_summarizers: list[list[int]] = None, add_info: str = None):
        # 构建分析面板数据
        dashboard = AnalyticsDashboardModel(
            name=name, execution=execution, combin_summarizer_ids=combin_summarizers,
            sup_summarizer_ids=sup_summarizers, focus=focus, add_info=add_info)
        g.session.add(dashboard)
        await g.session.flush()
        dashboard_id = dashboard.id

        # 所有组合摘要分析ID
        summarizer_ids = list(dict.fromkeys(chain.from_iterable(combin_summarizers)))

        # 所有摘要分析ID/mapping映射
        if sup_summarizers:
            summarizer_ids += list(dict.fromkeys(chain.from_iterable(sup_summarizers)))
            summarizer_ids = list(set(summarizer_ids))
        all_summarizers = await Summarizer.get_all(summarizer_ids=summarizer_ids)
        smz_id_mapping = {s["summarizer_id"]: s for s in all_summarizers}

        # 补充摘要分析的tag映射
        sup_tags_mapping = {}
        if sup_summarizers:
            support_summarizer_ids = dict.fromkeys(chain.from_iterable(sup_summarizers))
            for sup_smz_id in support_summarizer_ids:
                s = smz_id_mapping[sup_smz_id]
                if not s["tags"]:
                    continue
                for tag in s["tags"]:
                    if tag in sup_tags_mapping:
                        sup_tags_mapping[tag].append(s)
                    else:
                        sup_tags_mapping[tag] = [s]

        # 构建数据项数据
        for item_combin_smz_ids in product(*combin_summarizers):
            item_combin_smz_ids = list(item_combin_smz_ids)  # tuple -> list
            item_sup_smz_ids = []

            combin_tags = list(dict.fromkeys(chain.from_iterable(
                [smz_id_mapping[s_id]["tags"] for s_id in item_combin_smz_ids]
            )))
            for tag in combin_tags:
                if tag not in sup_tags_mapping:
                    continue
                for s in sup_tags_mapping[tag]:
                    if s["summarizer_id"] not in item_combin_smz_ids and s["summarizer_id"] not in item_sup_smz_ids:
                        item_sup_smz_ids.append(s["summarizer_id"])

            names = [smz_id_mapping[s_id]["name"] for s_id in item_combin_smz_ids+item_sup_smz_ids]

            g.session.add(
                AnalyticsItemModel(
                    dashboard_id=dashboard_id,
                    name="*".join(names)[:250],
                    combin_summarizer_ids=item_combin_smz_ids,
                    sup_summarizer_ids=item_sup_smz_ids)
            )

        # 构建分析指标数据
        for m in metrics:
            g.session.add(
                AnalyticsMetricModel(
                    dashboard_id=dashboard_id,
                    name=m["name"],
                    type_=m["type_"],
                    min_=m["min_"],
                    max_=m["max_"],
                    require_=m["require_"])
            )

        return dashboard_id

    async def update(self, dashboard_id: int, name: str = None, execution: str = None, focus: str = None,
                     combin_summarizers: list[list[int]] = None, metrics: list[dict] = None,
                     sup_summarizers: list[list[int]] = None, add_info: str = None):
        update_info = {}
        if name:
            update_info[AnalyticsDashboardModel.name] = name
        if execution:
            update_info[AnalyticsDashboardModel.execution] = execution
        if focus:
            update_info[AnalyticsDashboardModel.focus] = focus
        # if combin_summarizers:
        #     update_info[AnalyticsDashboardModel.combin_summarizer_ids] = combin_summarizers
        # if sup_summarizers:
        #     update_info[AnalyticsDashboardModel.sup_summarizer_ids] = sup_summarizers
        if add_info is not None:
            update_info[AnalyticsDashboardModel.add_info] = add_info
        # todo: 补充摘要分析可以增删,后续添加相关逻辑

        query = (update(AnalyticsDashboardModel)
                 .where(AnalyticsDashboardModel.id == dashboard_id)
                 .values(update_info))
        await g.session.execute(query)

        if metrics is not None:
            await self.delete_metrics(dashboard_id=dashboard_id)
            for m in metrics:
                await g.session.merge(
                    AnalyticsMetricModel(
                        id=m["metric_id"],
                        dashboard_id=dashboard_id,
                        name=m["name"],
                        type_=m["type_"],
                        min_=m["min_"],
                        max_=m["max_"],
                        require_=m["require_"],
                        is_delete=False)
                    )

    async def delete(self, dashboard_id: int):
        query = (update(AnalyticsDashboardModel)
                 .where(AnalyticsDashboardModel.id == dashboard_id)
                 .values({AnalyticsDashboardModel.is_delete: 1}))
        await g.session.execute(query)

        await self.delete_metrics(dashboard_id=dashboard_id)
        await self.delete_items(dashboard_id=dashboard_id)

    @staticmethod
    async def delete_metrics(dashboard_id: int):
        query = (update(AnalyticsMetricModel)
                 .where(AnalyticsMetricModel.dashboard_id == dashboard_id)
                 .values({AnalyticsMetricModel.is_delete: 1}))
        await g.session.execute(query)

    @staticmethod
    async def delete_items(dashboard_id: int):
        query = (update(AnalyticsItemModel)
                 .where(AnalyticsItemModel.dashboard_id == dashboard_id)
                 .values({AnalyticsItemModel.is_delete: 1}))
        await g.session.execute(query)

    @staticmethod
    def get_dashboard_analyze_task_id(analyze_task_id: int):
        return f"analyze_task_{analyze_task_id}"

    async def send_crontab_task(self, dashboard_id: int, crontab: str):
        cs = crontab.split(" ")
        schedule = (
            await self.dashboard_analyze.kicker()
            .with_schedule_id(self.get_dashboard_analyze_task_id(dashboard_id))
            .schedule_by_cron(
                redis_schedule_source,
                cron=CronSpec(minutes=cs[0], hours=cs[1], days=cs[2], months=cs[3], weekdays=cs[4], offset=TZ.zone),
                dashboard_id=dashboard_id
            )
        )
        logger.info(f"Analyze Dashboard[{dashboard_id}]: 定时摘要任务已发送 {schedule.schedule_id}")

    async def delete_crontab_task(self, dashboard_id: int):
        schedule_id = self.get_dashboard_analyze_task_id(dashboard_id)
        await redis_schedule_source.delete_schedule(schedule_id)
        logger.info(f"Analyze Dashboard[{dashboard_id}]: 定时摘要任务已删除 {schedule_id}")


    # tasks.dashboard_analyze的占位方法
    @staticmethod
    @broker.task(task_name="dashboard_analyze")
    async def dashboard_analyze(dashboard_id: int): ...


Dashboard = AnalyticsDashboardController()
