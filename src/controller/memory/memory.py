#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re

import json_repair

from controller.engine.chat import Chat<PERSON><PERSON><PERSON>, LLMModel, ChatHistory
from controller.memory.prompts import USER_MEMORY_PROMPT


class MemoryController:
    def __init__(self, model_name: LLMModel = LLMModel.QWEN3_30B):
        self.model_name = model_name
        self.system_prompt = USER_MEMORY_PROMPT

    async def auto_extract(self, query: str, memories: list[dict[str, str]] = None):
        chat_engine = ChatEngine(model_name=self.model_name)
        if memories is None:
            memories = []

        res = await chat_engine.generator(
            system_prompt=self.system_prompt,
            prompt=f"问句：{query.strip()}。\n已知要素：{memories}",
            temperature=0)
        content = res.choices[0].message.content.strip()
        if "<think>" in content:
            content = re.sub(r"<think>(.*?)</think>", "", content, flags=re.DOTALL)
        return json_repair.loads(content.strip())


Memory = MemoryController()


if __name__ == '__main__':
    import asyncio
    res1 = asyncio.run(Memory.auto_extract("我关注上海楼市、房价、大A等消息，给我一个相关的研究报告"))
    print(res1)
    res2 = asyncio.run(Memory.auto_extract("记住我只关注市级消息，不要给我市级以下消息", res1))
    print(res2)
