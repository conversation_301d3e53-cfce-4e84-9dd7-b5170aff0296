from common.time import now_tz_datestring
from common.snowflake import <PERSON><PERSON><PERSON>
from config import ES_CONFLICT_RETRY
from engine.es import es
from model.session import ChatSession, ChatHistory, CHAT_SESSION_INDEX, ChatSessionType, RetrieveChunkModel


class SessionController:
    @staticmethod
    async def get_one(session_id: str) -> ChatSession:
        res = await es.get(index=CHAT_SESSION_INDEX, id=session_id)
        return ChatSession.model_validate(res["_source"])

    @staticmethod
    async def create(session_type: ChatSessionType) -> ChatSession:
        session_id = str(Snowflake.get_id())
        session_item = ChatSession(
            session_id=session_id,
            session_type=session_type,
            create_time=now_tz_datestring(),
            chat_history=[],
        )
        await es.index(index=CHAT_SESSION_INDEX, id=session_id, body=session_item.model_dump(), refresh=True)
        return session_item

    @staticmethod
    async def append_to_chat_history(session_id: str, chat_history: ChatHistory, refresh: bool = False):
        update_body = {
            "script": {
                "source": """
                    ctx._source.chat_history.add(params.chat_record);
                    ctx._source.update_time = params.update_time;
                """,
                "lang": "painless",
                "params": {
                    "chat_record": chat_history.model_dump(exclude_none=True),
                    "update_time": now_tz_datestring()
                }
            }
        }

        await es.update(index=CHAT_SESSION_INDEX, id=session_id, body=update_body,
                        refresh=refresh, retry_on_conflict=ES_CONFLICT_RETRY)


Session = SessionController()
