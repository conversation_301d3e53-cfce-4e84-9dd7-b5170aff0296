import asyncio
import time
from typing import List

from deepseek_tokenizer import ds_token as tokenizer
from openai import Async<PERSON><PERSON>A<PERSON>

from config import LLM_API_URL, LLM_API_KEY, LLMModel
from common.logger import logger
from controller.operator.runner.base import TokenConsumption
from model.session import ChatHistory


ChatModelConnection = AsyncOpenAI(base_url=LLM_API_URL, api_key=LLM_API_KEY)


class ChatEngine:
    def __init__(self, model_name: str | LLMModel = LLMModel.GPT_4O, token_consumption: TokenConsumption | None = None,
                 base_url: str = None, api_key: str = None):
        self.chat_model = ChatModelConnection
        self.base_url = base_url
        self.api_key = api_key
        self.model_name = model_name
        self.token_consumption = token_consumption if token_consumption else TokenConsumption()
        self.input_token = 0
        self.output_token = 0

    async def generator(self, prompt: str, system_prompt: str = None, history: List[ChatHistory] = None,
                        stream: bool = False, **kwargs):
        messages, token = [], prompt
        task = asyncio.current_task()
        start_time = time.time()

        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
            token += system_prompt

        if history:
            for chat_message in history:
                if chat_message.error_msg is None and chat_message.is_delete is False:
                    if chat_message.user:
                        messages.append({"role": "user", "content": chat_message.user})
                        token += chat_message.user
                    if chat_message.assistant:
                        messages.append({"role": "assistant", "content": chat_message.assistant})
                        token += chat_message.assistant

        if prompt:
            messages.append({"role": "user", "content": prompt})

        if self.base_url:
            chat_model = AsyncOpenAI(base_url=self.base_url, api_key=self.api_key)
        else:
            chat_model = self.chat_model

        self.input_token += sum(len(tokenizer.encode(message["content"])) for message in messages)

        response = await chat_model.chat.completions.create(
            model=self.model_name,
            messages=messages,
            stream=stream,
            extra_body={"enable_thinking": False},
            **kwargs
        )

        if self.base_url:
            await chat_model.close()

        if not stream:
            content = response.choices[0].message.content.strip()
            self.output_token += len(tokenizer.encode(content))
            self.token_consumption.input_token.append(self.input_token)
            self.token_consumption.output_token.append(self.output_token)

            logger.info(
                f"LLM call: {self.__class__.__name__} {task.get_name()} is success after {time.time() - start_time:.2f} seconds. "
                f"Input tokens: {self.input_token}, Output tokens: {self.output_token}.")

        return response


if __name__ == '__main__':
    chat_engine = ChatEngine(model_name=LLMModel.QWEN3_32B)
    async def test():
        history = [
            ChatHistory(user="说说你的爱好", assistant="我的爱好是钓鱼"),
        ]
        resp = await chat_engine.generator("再说一遍你的爱好？/no_think", history=history, stream=True)
        async for message in resp:
            print(message)

    # asyncio.run(test())
    res = asyncio.run(chat_engine.generator("你是谁/no_think"))
    print(res.choices[0].message.content.strip())
    print(res.choices[0].message.model_dump().get("reasoning_content", ""))
