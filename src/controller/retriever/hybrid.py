#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import uuid
import asyncio
from typing import List

from config import RerankerModel, EmbeddingModel
from engine.es import es
from model.doc import get_index
from common.logger import logger
from controller.engine import EmbeddingEngine, RerankEngine, ChatHistory
from controller.retriever.base import BaseRetriever, async_time_cost
from controller.retriever.rewrite import RewriteEngine, LLMModel
from controller.retriever.web import WebRetriever
from controller.operator.chunking.base import RetrieveChunkModel


class HybridRetrieval(BaseRetriever):
    """混合检索系统，结合BM25和向量检索方法进行文档搜索
    
    该类实现了一个混合检索系统，通过组合BM25关键词匹配和向量相似度计算两种方式
    对文档进行检索和排序，以提高检索的准确性和多样性。
    """
    def __init__(self,
                 request_id: str = None,
                 repo_search: bool = True,
                 web_search: bool = False,
                 repo_ids: list[int] = None,
                 doc_ids: list[int] = None,
                 custom_boost: list[dict] = None,
                 max_doc_size: int = 10,
                 max_doc_chunk_size: int = None,
                 bm25_weight: float = 1,
                 embedding_weight: float = 1,
                 rewrite_engine: RewriteEngine | None = RewriteEngine(LLMModel.QWEN3_30B),
                 embedding_engine: EmbeddingEngine | None = EmbeddingEngine(EmbeddingModel.BGE_M3),
                 rerank_engine: RerankEngine | None = RerankEngine(RerankerModel.BGE_RERANKER_V2_M3),
                 rerank_max_size: int = 30,
                 rerank_threshold: float = 0.2,
                 topn: int = 20,
                 keep_repo_size: int = 5):
        """构建混合检索
        todo: 时间序列的权重控制

        Args:
            request_id: 检索请求的唯一标识符，如果为None则自动生成
            repo_ids: 待检索的知识库ID列表
            doc_ids: 待检索的文档ID列表
            custom_boost: 文档召回的boost要求
            max_doc_size: 保留最多多少个文档筛选范围 必须非None
            max_doc_chunk_size: 每个文档保留最多多少片段数, None时自动计算
            bm25_weight: BM25检索结果的权重系数，默认为1
            embedding_weight: 向量检索结果的权重系数，默认为1
            rewrite_engine: 问题改写引擎实例
            embedding_engine: 文本向量化引擎实例
            rerank_engine: 检索结果重排序引擎实例
            rerank_max_size: rerank engine放入的片段数,无rerank_engine则无效
            rerank_threshold: rerank engine的保留阈值,无rerank_engine则无效
            web_search: 是否启用网络搜索
            topn: 最终最大片段数量

        """
        if bm25_weight < 0 or embedding_weight < 0 or (bm25_weight + embedding_weight) < 0:
            raise ValueError("bm25_weight/embedding_weight必须>=0, 且加和>0")
        if (not embedding_engine) and embedding_weight > 0:
            logger.warning("由于没有embedding_engine, embedding_weight将被强制等于0")

        request_id = request_id or uuid.uuid4().hex
        super().__init__(
            request_id=request_id,
            rewrite_engine=rewrite_engine,
            embedding_engine=embedding_engine,
            rerank_engine=rerank_engine,
            rerank_max_size=rerank_max_size,
            rerank_threshold=rerank_threshold,
            log_prefix=f"混合召回[{request_id}] ",
            bm25_weight=bm25_weight,
            embedding_weight=embedding_weight,
            custom_boost=custom_boost
        )

        self.repo_search = repo_search
        self.web_search = web_search
        self.repo_ids = repo_ids
        self.doc_ids = doc_ids
        self.max_doc_size = max_doc_size
        self.max_doc_chunk_size = max_doc_chunk_size
        self.topn = topn
        self.keep_repo_size = keep_repo_size
        self.index = get_index(repo_ids=repo_ids)  # 索引index范围

    @async_time_cost("召回总耗时")
    async def retrieve(self, query: str, history: list[ChatHistory] = None) -> List[RetrieveChunkModel]:
        self.query = query.strip()
        self.history = history
        logger.info(self.log_prefix + f"开始文档召回: {self.query}")

        await self.rewriting()

        tasks = []
        if self.repo_search:
            tasks.append(self.searching())
        if self.web_search:
            web_retriever = WebRetriever(
                request_id=self.request_id,
                max_doc_chunk_size=self.max_doc_chunk_size,
                topn=self.topn,
                bm25_weight=self.bm25_weight,
                custom_boost=self.custom_boost
            )
            web_searching = web_retriever.searching(
                query=self.query,
                query_rewrite=self.query_rewrite,
                query_keywords=self.query_keywords,
                query_associative_keywords=self.query_associative_keywords,
                query_search_terms=self.query_search_terms,
                query_token_weight=self.query_token_weight)
            tasks.append(web_searching)

        tasks_result = await asyncio.gather(*tasks)
        source_chunks = []
        for task_result in tasks_result:
            source_chunks.extend(task_result)

        source_chunks = self.coarse_ranking(source_chunks=source_chunks)
        source_chunks = await self.model_reranking(source_chunks=source_chunks)

        if self.repo_search and self.web_search and len(source_chunks) > self.topn and self.keep_repo_size:
            repo_count = 0
            for i, chunk in enumerate(source_chunks):
                if chunk.get("web_search") is False:
                    repo_count += 1
                self.chunks.append(chunk)

                if self.topn - repo_count == i + 1:
                    for j in range(i + 1, len(source_chunks)):
                        if source_chunks[j].get("web_search") is False:
                            self.chunks.append(source_chunks[j])
                    break

        self.chunks = self.chunks[:self.topn]

        return [RetrieveChunkModel(**chunk) for chunk in self.chunks]

    @async_time_cost()
    async def searching(self):
        await self.embedding()
        doc_scores = await self.filter_doc()
        if not doc_scores:
            logger.info(self.log_prefix + f"文档未命中,返回空列表")
            return []
        source_chunks = await self.search(doc_scores=doc_scores)

        return source_chunks

    @async_time_cost()
    async def filter_doc(self) -> dict:
        dsl_query = {
            "bool": {
                "should": [
                    {
                        "function_score": {
                            "query": {
                                "bool": {
                                    "should": [
                                        *self.match_doc_query_tokens(),
                                        *self.match_doc_query_keyword(),
                                        *self.match_doc_query_associative_keyword()
                                    ]
                                }
                            },
                            "functions": [
                                *self.function_score_doc_multiply(),
                                *self.function_score_doc_custom()
                            ],
                            "boost_mode": "multiply"
                        }
                    }
                ],
                "filter": self.build_doc_filters(),
            },
        }
        # logging.info(f"DSL：{json.dumps(dsl_query, ensure_ascii=False, indent=4)}")
        res = await es.search(
            index=self.index,
            query=dsl_query,
            source_includes=[
                "doc_id",
                "filename",
            ],
            size=self.max_doc_size,
            ignore_unavailable=True
        )
        # logging.warning(json.dumps(res.body, ensure_ascii=False, indent=4))

        doc_scores = {}
        for doc in res["hits"]["hits"]:
            # res = await es.explain(index=index, query=dsl_query, id=doc["_source"]["doc_id"])
            doc_scores[doc["_source"]["doc_id"]] = doc["_score"] if isinstance(doc["_score"], float) else self._es_max_score

        logger.info(self.log_prefix + f"粗筛文档范围: {[doc_scores]}")
        return doc_scores

    async def search(self, doc_scores: dict[int, float]):
        conditions = []
        if self.bm25_weight > 0:
            conditions.extend(self.match_chunk_keyword())
            conditions.extend(self.match_chunk_associative_keyword())
        if self.embedding_weight > 0:
            conditions.extend(self.script_cosine_similarity())

        inner_size = self.max_doc_chunk_size or self.topn // len(doc_scores)
        if inner_size > 100:
            inner_size = 100
        if inner_size < 2:
            inner_size = 2

        dsl = {
            "bool": {
                "should": [
                    {
                        "nested": {
                            "path": "chunks",
                            "score_mode": "max",
                            "query": {
                                "bool": {
                                    "should": [
                                        {
                                            "function_score": {
                                                "query": {
                                                    "bool": {
                                                        "should": conditions
                                                    }
                                                },
                                                "functions": [
                                                    *self.function_score_chunk_boost(doc_scores=doc_scores),
                                                    # *self.function_score_chunk_multiply(),
                                                    *self.function_score_chunk_custom(),
                                                ],
                                                "boost_mode": "multiply"
                                            },
                                        }
                                    ],
                                    # "must_not": {"terms": {}}  不需要的类型等chunk内的条件
                                }
                            },
                            "inner_hits": {
                                "_source": [
                                    "chunks.cid",
                                    "chunks.title",
                                    "chunks.html_content",
                                    "chunks.plain_content",
                                    "chunks.xpath",
                                    "chunks.token_counts",
                                ],
                                # 每个文档几个片段
                                "size": inner_size
                            }
                        }
                    }
                ],
                "filter": [
                    {"terms": {"doc_id": [doc_id for doc_id in doc_scores.keys()]}}
                ],
                "minimum_should_match": 1
            }
        }

        res = await es.search(
            index=self.index,
            query=dsl,
            source_includes=[
                "doc_id",
                "filename",
                "data_time"
            ],
            size=self.max_doc_size,
            ignore_unavailable=True
        )
        source_data = []
        for doc in res["hits"]["hits"]:
            self.post_process_doc_annealing(hits=doc["inner_hits"]["chunks"]["hits"]["hits"])
            for i, chunk in enumerate(doc["inner_hits"]["chunks"]["hits"]["hits"]):
                # print(doc["_score"], window["_score"], window["_source"]["content"][:50].replace("\n", ""))
                if chunk["_score"] == "Infinity":
                    chunk["_score"] = self._es_max_score

                source_data.append(
                    {
                        **doc["_source"],
                        **chunk["_source"],
                        "score": chunk["_score"],
                        "web_search": False,
                    }
                )
        source_data = list(sorted(source_data, key=lambda x: x["score"], reverse=True))
        return source_data

    def build_doc_filters(self):
        filters = []
        if self.repo_ids is not None:
            filters.append({"terms": {"repo_id": self.repo_ids}})
        if self.doc_ids is not None:
            filters.append({"terms": {"doc_id": self.doc_ids}})
        return filters

    def script_cosine_similarity(self):
        """
        [分片筛选策略] Embedding策略,受embedding_weight影响权重
        Returns:

        """
        return [{
            "script_score": {
                "query": {
                    "match_all": {}
                },
                "script": {
                    "source": "cosineSimilarity(params.query_vector, 'chunks.vector')",
                    "params": {
                        "query_vector": self.query_vector
                    }
                },
                "boost": round(6 * self.embedding_weight, 2)
            }
        }]




if __name__ == '__main__':
    # from controller.user_config import UserRetrieve
    # from engine.rdb import g, session_maker
    # g.session = session_maker()
    # function_score = asyncio.run(UserRetrieve.get_function_score(user_id=1))
    # print(function_score)
    retrieval = HybridRetrieval(web_search=True)
    chunks = asyncio.run(retrieval.retrieve(query="宁德时代和中石油有哪些合作"))
    print(chunks)