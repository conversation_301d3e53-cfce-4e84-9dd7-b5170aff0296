#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from tavily import AsyncTavilyClient
from pydantic import BaseModel, Field

from config import TVLY_APIKEY
from common.logger import logger
from controller.retriever.web.base import WebRetrieverBase, SearchResult


class TvlySearchConfig(BaseModel):
    class SearchRecency(StrEnum):
        oneDay = "oneDay"
        oneWeek = "oneWeek"
        oneMonth = "oneMonth"
        oneYear = "oneYear"
        noLimit = "noLimit"

    extract: Annotated[bool, Field(title="是否进行内容提取")] = False
    domain: Annotated[Optional[str], Field(title="指定域名")] = None
    recency: Annotated[SearchRecency, Field(title="搜索时效")] = SearchRecency.noLimit
    count: Annotated[int, Field(title="返回结果数量", ge=1, le=30)] = 10


class TvlyWebRetriever(WebRetrieverBase):
    async def web_search(self):
        limit: int = 10
        score_threshold: float = 0.4
        try:
            tvly_client = AsyncTavilyClient(TVLY_APIKEY)
            response = await tvly_client.search(
                query=self.query,
                max_results=limit
            )
        except Exception as e:
            logger.error(self.log_prefix + f"[Tavily]网络搜索失败: query: {self.query} error: {str(e)}")
            return []
        else:
            results = [SearchResult(
                title=sr["title"],
                content=sr["content"],
                url=sr["url"],
                score=sr["score"]
            ) for sr in response["results"] if sr["score"] >= score_threshold]
            logger.info(self.log_prefix + f"[Tavily]网络搜索成功: query: {self.query} 找到{len(results)}/{limit}个相关结果")
            return results