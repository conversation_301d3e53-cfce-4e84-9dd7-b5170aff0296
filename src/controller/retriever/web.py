#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import uuid
import asyncio

from tavily import AsyncTavilyClient

from config import TVLY_APIKEY
from common.logger import logger
from engine.es import es
from model.doc import WEB_DOC_INDEX
from controller.parser import WebParser
from controller.retriever.base import BaseRetriever
from controller.engine import ChatHistory


class WebRetriever(BaseRetriever):
    def __init__(self,
                 request_id: str = None,
                 max_doc_chunk_size: int = None,
                 topn: int = 10,
                 bm25_weight: float = 1,
                 custom_boost: list[dict] = None):
        request_id = request_id or uuid.uuid4().hex
        super().__init__(
            request_id=request_id,
            log_prefix=f"网络搜索[{request_id}] ",
            bm25_weight=bm25_weight,
            custom_boost=custom_boost,
        )

        self.search_results = []
        self.doc_ids = []
        self.max_doc_chunk_size = max_doc_chunk_size
        self.topn = topn
        self.index = WEB_DOC_INDEX

    async def retrieve(self, query: str, history: list[ChatHistory] = None):
        # todo: 此方法未完全完成,请勿使用
        self.query = query.strip()
        self.history = history
        logger.info(self.log_prefix + f"开始文档召回: {self.query}")

        await self.rewriting()

        self.search_results = await self.tvly_search()
        if not self.search_results:
            return []

        self.doc_ids = await self.parsing()
        logger.info(self.log_prefix + f"网络搜索解析完成: {self.doc_ids}")
        if not self.doc_ids:
            return []

        doc_scores = {doc_id: 1 for doc_id in self.doc_ids}
        source_chunks = await self.search(doc_scores=doc_scores)
        return source_chunks

    async def searching(self,
                        query: str,
                        query_rewrite: str = None,
                        query_keywords: list[str] | None = None,
                        query_associative_keywords: str | None = None,
                        query_search_terms: str | None = None,
                        query_token_weight: dict | None = None):
        # 导入rewrite结果
        if query_rewrite is None:
            self.query_rewrite = query_rewrite
        if query_keywords is None:
            self.query_keywords = query_keywords
        if query_associative_keywords is None:
            self.query_associative_keywords = query_associative_keywords
        if query_search_terms is None:
            self.query_search_terms = query_search_terms
        if query_token_weight is None:
            self.query_token_weight = query_token_weight
        self.query = query.strip()
        logger.info(self.log_prefix + f"开始网络召回: {self.query}")

        self.search_results = await self.tvly_search()
        if not self.search_results:
            return []

        doc_scores = await self.parsing()
        logger.info(self.log_prefix + f"网络搜索解析完成: {self.doc_ids}")
        if not self.doc_ids:
            return []

        source_chunks = await self.search(doc_scores=doc_scores)
        return source_chunks

    async def search(self, doc_scores: dict[int, float]):
        conditions = []
        conditions.extend(self.match_chunk_keyword())
        conditions.extend(self.match_chunk_associative_keyword())

        inner_size = self.max_doc_chunk_size or self.topn // len(doc_scores)
        if inner_size > 100:
            inner_size = 100
        if inner_size < 2:
            inner_size = 2

        dsl = {
            "bool": {
                "should": [
                    {
                        "nested": {
                            "path": "chunks",
                            "score_mode": "max",
                            "query": {
                                "bool": {
                                    "should": [
                                        {
                                            "function_score": {
                                                "query": {
                                                    "bool": {
                                                        "should": conditions
                                                    }
                                                },
                                                "functions": [
                                                    *self.function_score_chunk_boost(doc_scores=doc_scores),
                                                    *self.function_score_chunk_custom(),
                                                ],
                                                "boost_mode": "multiply"
                                            },
                                        }
                                    ],
                                    # "must_not": {"terms": {}}  不需要的类型等chunk内的条件
                                }
                            },
                            "inner_hits": {
                                "_source": [
                                    "chunks.cid",
                                    "chunks.title",
                                    "chunks.html_content",
                                    "chunks.plain_content",
                                    "chunks.xpath",
                                    "chunks.token_counts",
                                ],
                                # 每个文档几个片段
                                "size": inner_size
                            }
                        }
                    }
                ],
                "filter": [
                    {"terms": {"doc_id": [doc_id for doc_id in doc_scores.keys()]}}
                ],
                "minimum_should_match": 1
            }
        }

        res = await es.search(
            index=self.index,
            query=dsl,
            source_includes=[
                "doc_id",
                "filename",
                "data_time",
                "url"
            ],
            size=len(self.doc_ids),
            ignore_unavailable=True
        )
        source_data = []
        for doc in res["hits"]["hits"]:
            self.post_process_doc_annealing(hits=doc["inner_hits"]["chunks"]["hits"]["hits"])
            for i, chunk in enumerate(doc["inner_hits"]["chunks"]["hits"]["hits"]):
                # print(doc["_score"], window["_score"], window["_source"]["content"][:50].replace("\n", ""))
                if chunk["_score"] == "Infinity":
                    chunk["_score"] = self._es_max_score

                source_data.append(
                    {
                        **doc["_source"],
                        **chunk["_source"],
                        "score": chunk["_score"],
                        "web_search": True,
                    }
                )
        source_data = list(sorted(source_data, key=lambda x: x["score"], reverse=True))
        return source_data

    async def parsing(self):
        parse_runners = []
        doc_ids = []
        doc_scores = {}
        for sr in self.search_results:
            runner = WebParser(title=sr["title"], url=sr["url"], content=sr["content"])
            parse_runners.append(runner.exec())
            doc_scores[runner.doc_id] = sr["score"]
            doc_ids.append(runner.doc_id)

        await asyncio.gather(*parse_runners)
        self.doc_ids = doc_ids

        return doc_scores

    async def tvly_search(self, limit: int = 10, score_threshold: float = 0.4):
        try:
            tvly_client = AsyncTavilyClient(TVLY_APIKEY)
            response = await tvly_client.search(
                query=self.query,
                max_results=limit
            )
        except Exception as e:
            logger.error(self.log_prefix + f"网络搜索失败: query: {self.query} error: {str(e)}")
            return []
        else:
            results = [r for r in response["results"] if r["score"] >= score_threshold]
            logger.info(self.log_prefix + f"网络搜索成功: query: {self.query} 找到{len(results)}/{limit}个相关结果")
            return results


if __name__ == '__main__':
    web_retrieve = WebRetriever()
    res = asyncio.run(web_retrieve.searching("浦银理财是做什么的？"))
    print(res)