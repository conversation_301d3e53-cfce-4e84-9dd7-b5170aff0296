#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import hashlib
import asyncio
import chardet

import httpx
from controller.parser.chunker.tokenizer import BGE_M3_TOKENIZER
from elasticsearch import AsyncElasticsearch

from config import ES_URL
from common.logger import logger
from common.time import now_tz_datestring
from controller.parser.chunker import HtmlChunker
from controller.repository import Doc<PERSON>tatus
from controller.parser.base import ParserBase
from model.doc import WEB_DOC_INDEX

# 这里需要单独构建一个ES,避免其retry参数
es = AsyncElasticsearch(ES_URL, request_timeout=15, max_retries=0)

class WebParser(ParserBase):
    def __init__(self, url: str, title: str, content: str):
        super().__init__(
            doc_id=self.url_to_doc_id(url=url),
            chunker=HtmlChunker(tokenizer=BGE_M3_TOKENIZER),
            embedding_engine=None,
            embedding_batch_size=0)
        self.url = url
        self.title = title
        self.content = content

        self.index = WEB_DOC_INDEX

        self.log_prefix = f"Doc[{self.doc_id}]网页解析 "
        self.http_timeout = httpx.Timeout(6)

        # 默认分片字段权重
        self.chunk_field_boost = {
            "chunks.title": 2,
            "chunks.plain_content": 1,
        }

    async def exec(self):
        try:
            html = await self.crawl()
            if not html:
                raise ValueError("未找到html文本内容")
        except Exception as err:
            logger.error(self.log_prefix + f"爬取失败: {self.url} {str(err)}")
            html = f"""<!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>{self.title}</title>
            </head>
            <body>
                <p>{self.content}</p>
            </body>
            </html>"""

        self.chunks = self.chunker.chunk(html_content=html)
        self.html = html
        await self.merge_plain_text()
        await self.es_data_index()

    async def crawl(self):
        async with httpx.AsyncClient(
                transport=httpx.AsyncHTTPTransport(retries=3),
                timeout=self.http_timeout
        ) as client:
            response = await client.get(url=self.url, follow_redirects=True)
            # 使用 response.text 自动处理编码，或者手动检测编码
            if response.encoding is None:
                # 如果没有检测到编码，尝试从内容中检测
                detected = chardet.detect(response.content)
                response.encoding = detected["encoding"] or "utf-8"
            return response.text

    async def es_data_index(self):
        logger.info(f"{self.log_prefix}执行ES文档构建上传")
        chunks = [
            {
                "cid": f"{self.doc_id}_{i}",
                "index": i,
                "title": chunk.title,
                "html_content": "".join([node.html_content for node in chunk.nodes]),
                "origin_plain_content": chunk.origin_plain_content,
                "plain_content": chunk.plain_content,
                "xpath": [node.xpath for node in chunk.nodes],
                "token_counts": chunk.token_counts,
                "start_offset": chunk.start_offset,
                "end_offset": chunk.end_offset
            }
            for i, chunk in enumerate(self.chunks)
        ]

        es_doc = {
            "doc_id": self.doc_id,
            "repo_id": None,
            "filename": self.title,
            "status": DocStatus.rag_success if self.error is None else DocStatus.rag_fail,
            "tags": [],
            "title": self.title,
            "source": None,
            "url": self.url,
            "author": None,
            # 减小上传IO
            # "html": self.html,
            # "plain_text": self.plain_text,
            "chunks": chunks,
            "keywords": [],
            "data_time": None,
            "create_time": now_tz_datestring(),
        }
        await es.index(
            index=self.index,
            id=str(self.doc_id),
            document=es_doc,
            error_trace=True,
            refresh=True
        )

    @staticmethod
    def url_to_doc_id(url: str) -> int:
        """将URL转换为整数ID

        Args:
            url: 网页URL

        Returns:
            整数ID，用于唯一标识URL
        """
        # 使用 MD5 生成稳定的哈希值
        md5_hash = hashlib.sha256(url.encode("utf-8")).hexdigest()
        # 取前16位（64位）转为整数
        return int(md5_hash[:16], 16) + **********  # 增加10亿防止和知识库冲突


if __name__ == '__main__':
    parser = WebParser(url="https://www.jp.bankcomm.com/BankCommSite/upload/wmbooks/202312/19/**********.pdf",
                       title="浦银理财天添利现金宝29 号理财产品说明书 - 交通银行",
                       content="本理财产品的《投资协议书》、《销售（代理销售）协议书》、《产品说明书》、. 《风险揭示书》、《投资者权益须知》等文件及其不时有效修订与补充共同")
    asyncio.run(parser.exec())
