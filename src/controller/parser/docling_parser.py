#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re
import base64

import httpx

from config import DOCLING_SERVER_URL
from common.logger import logger, async_time_cost
from common.context import g
from controller.parser.base import Pa<PERSON><PERSON><PERSON><PERSON>, Chunker
from controller.parser.format_convert import LocalConverter
from controller.engine import EmbeddingEngine
from controller.repository import Doc, DocStatus, LibreofficePreConvertSupportSuffix, SpecialSupportSuffix


class DoclingParser(ParserBase):
    def __init__(self, doc_id: int,
                 chunker: Chunker,
                 embedding_engine: EmbeddingEngine,
                 embedding_batch_size: int = 10,
                 force_ocr: bool = True,
                 do_table_structure: bool = True,
                 do_formula_enrichment: bool = True,
                 include_images: bool = True,
                 images_scale: float = 2.0):
        super().__init__(doc_id=doc_id,
                         chunker=chunker,
                         embedding_engine=embedding_engine,
                         embedding_batch_size=embedding_batch_size)

        self.converter = LocalConverter
        self.parse_timeout = 3600

        # docling解析配置
        self.force_ocr = force_ocr
        self.do_table_structure = do_table_structure
        self.do_formula_enrichment = do_formula_enrichment
        self.include_images = include_images
        self.images_scale = images_scale

    async def parsing(self):
        await Doc.update(doc_id=self.doc_id, status=DocStatus.parsing)
        await g.session.commit()

        suffix = self.local_path.suffix

        if suffix in LibreofficePreConvertSupportSuffix.__members__.values():
            # 使用LibreOffice进行预转换
            await self._libreoffice_pre_convert()

        if suffix in SpecialSupportSuffix.__members__.values():
            if suffix == SpecialSupportSuffix.txt:
                self._txt_to_html()

        if not self.html:
            self.html = await self.docling_source_request()

        await self.upload_html()

        logger.info(f"{self.log_prefix}parsing阶段完成")

    @async_time_cost()
    async def docling_source_request(self) -> str | None:
        logger.info(f"{self.log_prefix}请求解析")
        with open(self.local_path.as_posix(), mode="rb") as f:
            pdf_data = f.read()
            b64_data = base64.b64encode(pdf_data).decode("utf-8")

        async with httpx.AsyncClient(timeout=self.parse_timeout) as client:
            url = f"{DOCLING_SERVER_URL}/v1alpha/convert/source"
            payload = {
                "options": {
                    "from_formats": ["docx", "pptx", "html", "image", "pdf", "asciidoc", "md", "xlsx"],
                    "to_formats": ["html"],
                    "image_export_mode": "embedded",
                    "do_ocr": True,
                    "ocr_engine": "rapidocr",
                    "ocr_lang": ["english", "chinese"],
                    "pdf_backend": "dlparse_v4",
                    "table_mode": "accurate",
                    "abort_on_error": False,
                    "return_as_file": False,
                    "force_ocr": self.force_ocr,
                    "include_images": self.include_images,
                    "images_scale": self.images_scale,
                    "do_table_structure": self.do_table_structure,
                    "do_formula_enrichment": self.do_formula_enrichment,
                },
                "file_sources": [
                    {
                        "base64_string": b64_data,
                        "filename": self.s3_path.name
                    }
                ]
            }
            try:
                response = await client.post(url=url, json=payload)
                response.raise_for_status()
            except Exception as err:
                logger.error(f"{self.log_prefix}解析失败: {err}")
                raise err

            html_content = response.json()["document"]["html_content"]

            if "\\u003C" in html_content:  # 检测是否包含Unicode转义序列
                html_content = html_content.encode().decode("unicode_escape")

            # 将纯None字符转为空格,多出现在表格中,docling会使用该字符串进行占位的显式提示
            html_content = re.sub(r">(\s*None\s*)<", r"> <", html_content)

            return html_content

    def _txt_to_html(self):
        with open(self.local_path, mode="r", encoding="utf-8") as f:
            lines = f.readlines()

        html_content = "\n".join(f"<p>{line}</p>" for line in lines)

        self.html = html_content
        # 注意这里没上传,并且html文件等待被后续流程覆写

    @async_time_cost()
    async def _libreoffice_pre_convert(self):
        """
        使用LibreOffice将旧格式文件转换为现代格式
        支持: .doc -> .docx, .xls -> .xlsx, .ppt -> .pptx
        """
        suffix = self.local_path.suffix.lower()

        # 确定目标格式
        exporter = {
            ".doc": 'docx:"MS Word 2007 XML"',
            ".xls": "xlsx",
            ".ppt": "pptx"
        }[suffix]

        try:
            # 使用异步并发转换器进行转换
            converted_path = await self.converter.libreoffice_convert(
                input_path=self.local_path,
                output_dir=self.local_dir,
                exporter=exporter,
                log_prefix=self.log_prefix
            )

            # 更新本地路径为转换后的文件
            if converted_path.exists():
                self.local_path = converted_path
                logger.info(f"{self.log_prefix}LibreOffice预转换成功: {self.local_path} ")
            else:
                logger.error(f"{self.log_prefix}LibreOffice预转换失败: 找不到转换后的文件 {converted_path}")
                raise FileNotFoundError(f"Converted file not found: {converted_path}")

        except Exception as e:
            logger.error(f"{self.log_prefix}LibreOffice预转换失败: {e}")
            raise e
