#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re
import shutil
import asyncio
from pathlib import Path
from collections import Counter

import jieba.posseg as psg

from config.const import LocalMappingDir

from engine.file_system import minio_client, repository_bucket
from engine.rdb import g
from engine.es import es
from common.logger import logger
from common.time import now_tz_datestring
from controller.repository import Doc, DocStatus, get_index
from controller.parser.chunker.base import Chun<PERSON>, Chunker
from controller.engine import EmbeddingEngine


class ParserBase:
    keyword_filter_pattern = re.compile(r"^(?:\d+|[0-9\W]+|[a-zA-Z]{1,2}|[a-zA-Z]{1,2}\W*)$")
    meaningful_chars_pattern = re.compile(r"[\u4e00-\u9fffA-Za-z]")

    def __init__(self,
                 doc_id: int,
                 chunker: Chunker,
                 embedding_engine: EmbeddingEngine = None,
                 embedding_batch_size: int = 8):
        assert doc_id is not None, "解析初始化必须传入doc_id"

        self.doc_id = doc_id
        self.chunker = chunker
        self.embedding = embedding_engine
        self.embedding_batch_size = embedding_batch_size

        # 初始化
        self.error: Exception | None = None
        self.log_prefix = f"Doc[{doc_id}]解析 "

        # prepare阶段补充
        self.doc: dict | None = None
        self.s3_path: Path | None = None
        self.local_path: Path | None = None
        self.local_dir: Path | None = None

        # parsing阶段补充
        self.html: str | None = None
        self.plain_text: str = ""

        # chunking阶段补充
        self.chunks: list[Chunk] = []

        # post_process阶段补充
        self.file_title: str | None = None
        self.keywords: list[str] | None = None


    async def exec(self):
        await self.preparing()
        try:
            await self.parsing()
            await self.chunking()
            await self.post_processing()
            await self.data_processing()
        except Exception as err:
            self.error = err
            await self.data_processing()
            logger.exception(err)
            logger.error(f"{self.log_prefix}任务失败")
        else:
            logger.info(f"{self.log_prefix}任务成功")
        finally:
            await self.ending()

    async def load_doc(self):
        doc = await Doc.get_one(doc_id=self.doc_id)

        self.doc = doc
        self.s3_path = Path(doc["path"])

    async def preparing(self):
        await self.load_doc()
        await self.download()
        logger.info(f"{self.log_prefix}prepare阶段完成")

    async def parsing(self):
        ...  # parsing逻辑
        await Doc.update(doc_id=self.doc_id, status=DocStatus.parse_success)
        await g.session.commit()

        await self.upload_html()

        logger.info(f"{self.log_prefix}parsing阶段完成")

    async def chunking(self):
        self.chunks = self.chunker.chunk(html_content=self.html)
        logger.info(f"{self.log_prefix}chunking阶段完成")

    async def post_processing(self):
        if self.html is None:
            raise RuntimeError(f"{self.log_prefix}未解析出html内容")

        await self.add_splittable_symbol()
        await self.assign_chunk_offsets()
        await self.merge_plain_text()
        await self.detect_file_title()
        await self.get_keywords()
        await self.chunk_embedding()
        logger.info(f"{self.log_prefix}post_processing阶段完成")

    async def data_processing(self):
        await self.es_data_index()
        await self.rdb_data_update()
        logger.info(f"{self.log_prefix}data_processing阶段完成")

    async def ending(self):
        self.rm_local_dir()

    async def download(self):
        if self.s3_path is None:
            raise RuntimeError(f"{self.log_prefix}s3_path must be set")

        self.local_path = LocalMappingDir / self.s3_path.parent / f"{self.doc_id}{self.s3_path.suffix}"
        self.local_dir = self.local_path.parent

        if self.local_path.exists():
            logger.info(f"{self.log_prefix}文件已存在: {self.local_path.as_posix()} 跳过下载")
            return self.local_path

        logger.info(f"{self.log_prefix}下载文件: {self.s3_path.as_posix()} -> {self.local_path.as_posix()}")
        self.local_path.parent.mkdir(parents=True, exist_ok=True)

        def _download():
            minio_client.fget_object(
                bucket_name=repository_bucket,
                object_name=self.s3_path.as_posix(),
                file_path=self.local_path.as_posix()
        )

        await asyncio.to_thread(_download)

        return self.local_path

    @staticmethod
    async def upload(local_path: Path, s3_path: Path):
        def _upload():
            minio_client.fput_object(
                bucket_name=repository_bucket,
                object_name=s3_path.as_posix(),
                file_path=local_path.as_posix()
            )

        await asyncio.to_thread(_upload)

    async def detect_file_title(self):
        logger.info(f"{self.log_prefix}分析文档标题")
        file_title_type = None
        file_title = ""
        if self.chunks:
            # 在第一个chunks里招TOP 4的node
            for node in self.chunks[0].nodes[:4]:
                # 该节点无标题,跳过
                if node.title_type is None:
                    # 如果该节点无标题,但已确定标题类型,退出检查
                    if file_title_type is not None:
                        break
                    continue

                # 该节点有标题
                else:
                    # 且是首个标题,确定标题类型,写入标题
                    if file_title_type is None:
                        file_title_type = node.title_type
                        file_title += node.plain_content
                        continue

                    # 已有标题类型,比较节点标题与标题类型是否一致
                    else:
                        # 一致,追加写入
                        if file_title_type == node.title_type:
                            file_title += node.plain_content
                        # 不一致,退出检查
                        else:
                            break

        self.file_title = file_title if file_title else None

    async def upload_html(self):
        if not self.local_dir or not self.html:
            logger.info("未找到本地目录或html,跳过html上传")
            return

        html_filepath = self.local_dir / f"{self.doc_id}.html"
        with open(html_filepath, mode="w", encoding="utf-8") as f:
            f.write(self.html)

        logger.info(self.log_prefix + "上传html")
        await self.upload(
            local_path=self.local_dir / f"{self.doc_id}.html",
            s3_path=self.s3_path.parent / f"{self.doc_id}.html")

    async def get_keywords(self, topn: int = 5):
        logger.info(f"{self.log_prefix}获取文档关键词")
        tokens = []
        step = 10000
        index = get_index([self.doc["repo_id"]])
        text = "\n".join([chunk.plain_content for chunk in self.chunks])

        for batch_start in range(0, len(text), step):
            batch_text = text[batch_start:batch_start + step]
            response = await es.indices.analyze(
                index=index,
                body={
                    "analyzer": "ik_smart",
                    "text": batch_text,
                }
            )
            tokens.extend(
                [t["token"] for t in response["tokens"] if len(t["token"]) > 1 and not t["token"].isnumeric()])

        keywords = []
        counter_words = [item[0] for item in sorted(Counter(tokens).items(), key=lambda x: x[1], reverse=True)[:500]]
        for pair in psg.lcut(" ".join(counter_words)):
            word = pair.word.strip()
            if (len(word) > 1
                    and ("n" in pair.flag)
                    and word not in keywords
                    and (not self.keyword_filter_pattern.fullmatch(word))):
                keywords.append(word)
                if len(keywords) >= topn:
                    break

        self.keywords = keywords
        return keywords

    async def add_splittable_symbol(self):
        """如果前个chunk的尾节点,不等于后个chunk的首节点,则在前个chunk的尾节点追加换行符.主要为全文档纯文本构造做准备"""
        for i, chunk in enumerate(self.chunks):
            if i == 0:
                continue
            if self.chunks[i-1].nodes[-1].tag != chunk.nodes[0].tag and self.chunks[i-1].plain_content[-1] != "\n":
                self.chunks[i-1].origin_plain_content += "\n"

    async def merge_plain_text(self):
        for chunk in self.chunks:
            self.plain_text += chunk.plain_content

    async def assign_chunk_offsets(self):
        """
        根据plain_content计算并分配每一个chunk的start_offset和end_offset
        简单地按照chunk的顺序累计计算偏移量
        """
        if not self.chunks:
            logger.warning(f"{self.log_prefix}未找到chunks，跳过offset分配")
            return

        logger.info(f"{self.log_prefix}开始分配chunk偏移量")

        current_offset = 0

        for i, chunk in enumerate(self.chunks):
            chunk_content = chunk.origin_plain_content
            chunk_length = len(chunk_content)

            # 设置当前chunk的偏移量
            chunk.start_offset = current_offset
            chunk.end_offset = current_offset + chunk_length

            # 更新下一个chunk的起始位置
            current_offset = chunk.end_offset

        logger.info(f"{self.log_prefix}完成chunk偏移量分配，共处理 {len(self.chunks)} 个chunks")

    async def chunk_embedding(self):
        if self.embedding is None:
            logger.info(f"{self.log_prefix}未配置向量引擎,跳过请求向量")
            return

        logger.info(f"{self.log_prefix}请求向量")
        texts = []
        for chunk in self.chunks:
            if len(self.meaningful_chars_pattern.findall(chunk.plain_content)) > 4:
                texts.append(chunk.plain_content)

        text_vector_mapping = {}
        for step in range(0, len(texts), self.embedding_batch_size):
            batch_text = texts[step:step + self.embedding_batch_size]
            vectors = await self.embedding.encode(batch_text)
            logger.info(f"{self.log_prefix}embedding {min(step + self.embedding_batch_size, len(texts))}/{len(texts)}")
            for i, value in enumerate(vectors):
                text_vector_mapping[batch_text[i]] = value

        for chunk in self.chunks:

            if chunk.plain_content in text_vector_mapping:
                chunk.vector = text_vector_mapping[chunk.plain_content]
            else:
                chunk.vector = 1024 * [1]

    async def es_data_index(self):
        logger.info(f"{self.log_prefix}执行ES文档构建上传")
        chunks = [
            {
                "cid": f"{self.doc_id}_{i}",
                "index": i,
                "title": chunk.title,
                "html_content": "".join([node.html_content for node in chunk.nodes]),
                "origin_plain_content": chunk.origin_plain_content,
                "plain_content": chunk.plain_content,
                "xpath": [node.xpath for node in chunk.nodes],
                "token_counts": chunk.token_counts,
                "vector": chunk.vector,
                "start_offset": chunk.start_offset,
                "end_offset": chunk.end_offset
            }
            for i, chunk in enumerate(self.chunks)
        ]
        index = get_index(repo_ids=[self.doc["repo_id"]])

        es_doc = {
            "doc_id": self.doc_id,
            "repo_id": self.doc["repo_id"],
            "filename": self.doc["name"],
            "status": DocStatus.rag_success if self.error is None else DocStatus.rag_fail,
            "tags": [],
            "title": self.file_title,
            "source": None,
            "url": None,
            "author": None,
            "html": self.html,
            "plain_text": self.plain_text,
            "chunks": chunks,
            "keywords": self.keywords,
            "data_time": self.doc["create_time"],
            "create_time": now_tz_datestring(),
        }
        await es.update(
            index=index,
            id=self.doc_id,
            doc=es_doc,
            doc_as_upsert=True,
            error_trace=True,
            refresh=True
        )

    async def rdb_data_update(self):
        logger.info(f"{self.log_prefix}执行RDB doc表更新")
        await Doc.update(
            doc_id=self.doc_id,
            status=DocStatus.rag_success if self.error is None else DocStatus.rag_fail)
        await g.session.commit()

    def rm_local_dir(self):
        if self.local_dir and self.local_dir.exists():
            shutil.rmtree(self.local_dir)