#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from pathlib import Path

from transformers import AutoTokenizer


def make_tokenizer(model_path: Path, model_name):
    if model_path and model_path.exists():
        return AutoTokenizer.from_pretrained(model_path)
    else:
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        tokenizer.save_pretrained(model_path)
        return tokenizer

BGE_M3_TOKENIZER = make_tokenizer(model_name="BAAI/bge-m3", model_path=Path("../resources/bge_m3_tokenizer"))