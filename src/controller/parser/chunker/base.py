#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from enum import Str<PERSON><PERSON>
from typing import Optional

from lxml import etree, html as lxmlhtml
from pydantic import BaseModel, model_validator
from transformers import AutoTokenizer

from .tokenizer import BGE_M3_TOKENIZER


class TitleType(StrEnum):
    h1 = "h1"
    h2 = "h2"
    h3 = "h3"
    h4 = "h4"
    h5 = "h5"
    h6 = "h6"
    strong = "strong"
    span_strong = "span_strong"


class ChunkNode(BaseModel):
    tag: str
    xpath: str
    plain_content: str
    html_content: str
    token_counts: int
    title_type: TitleType = None

    origin_plain_content: Optional[str] = None

    @model_validator(mode="after")
    def tag_lower(self):
        self.tag = self.tag.lower()
        return self

    @model_validator(mode="after")
    def analyze_title_type(self):
        if self.tag.startswith("h") and self.tag in TitleType.__members__.values():
            self.title_type = TitleType(self.tag)
        elif len(self.plain_content) <= 24 and self.tag == "strong":
            self.title_type = TitleType.strong
        elif len(self.plain_content) <= 24 and self.tag == "span" and "strong" in self.html_content:
            self.title_type = TitleType.span_strong
        else:
            pass
        return self

    @model_validator(mode="after")
    def complete_origin_plain_content(self):
        if self.origin_plain_content is None:
            self.origin_plain_content = self.plain_content
        return self


class Chunk(BaseModel):
    nodes: list[ChunkNode] = []
    title: list[str] = []

    token_counts: int = 0
    plain_content: str = ""
    origin_plain_content: str = ""
    vector: list[int] = None
    start_offset: int = None
    end_offset: int = None


    def add_node(self, node: ChunkNode):
        # 标题节点数小于3
        if (len(self.title) < 3
                and len(self.nodes) < 3
                and node.title_type is not None
                and ((not self.nodes) or (self.nodes[-1]).title_type is not None)):
            self.title.append(node.plain_content)

        # 对于不同的标签,或新标签内容为空白时,视为换行.追加换行符
        # 否则不追加换行符
        if self.nodes:
            if self.nodes[-1].tag != node.tag or node.origin_plain_content.strip() == "":
                self.origin_plain_content += "\n"
                self.plain_content += "\n"

        self.origin_plain_content += node.origin_plain_content
        self.plain_content += node.plain_content.strip()

        self.token_counts += node.token_counts

        self.nodes.append(node)


class Chunker:
    def __init__(self,
                 tokenizer: AutoTokenizer = BGE_M3_TOKENIZER,
                 max_tokens: int = 1024,
                 max_tolerance_tokens: int = 2048):

        self.max_tokens = max_tokens
        self.max_tolerance_tokens = max_tolerance_tokens
        self.tokenizer = tokenizer

        self.chunks: list[Chunk] = []

    def chunk(self, html_content: str) -> list[Chunk]:...
