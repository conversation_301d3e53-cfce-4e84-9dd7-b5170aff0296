import re
from typing import List

from config import DEFAULT_DOC_CHUNK_SIZE, DEFAULT_ABSTRACT_CHUNK_SIZE, DEFAULT_ABSTRACT_OVERLAP_SIZE
from controller.operator.chunking import DocModel, DocChunking, SuggestChunking, ExtractModel
from controller.operator.runner.abstract import AbstractSuggest, AbstractGenerator, AbstractCombine, SuggestFormatFix
from controller.operator.workflow import BaseWorkflow


class AbstractWorkflow(BaseWorkflow):
    def __init__(self, doc_list: List[DocModel], user_prompt: str, doc_chunk_size: int = DEFAULT_DOC_CHUNK_SIZE):
        super().__init__()

        self.doc_chunking = DocChunking(chunk_size=doc_chunk_size)
        self.suggest_chunking = SuggestChunking(chunk_size=DEFAULT_ABSTRACT_CHUNK_SIZE,
                                                overlap_size=DEFAULT_ABSTRACT_OVERLAP_SIZE)
        self.doc_list = doc_list
        self.user_prompt = user_prompt

    @staticmethod
    def extract_suggest_numbers(text: str) -> List[int]:
        pattern = r'\[suggest:(\d+)\]'
        numbers = re.findall(pattern, text)
        numbers = list(map(int, numbers))
        return numbers

    async def _run(self):
        chunk_result = self.doc_chunking.chunk(self.doc_list)
        runners = [AbstractSuggest(
            user_prompt=self.user_prompt,
            chunk=chunk,
            token_consumption=self.token_consumption
        ) for chunk in chunk_result]
        abstract_suggest_result = await self.run_with_concurrency(runners)

        abstract_suggest_list = [item.model_dump() for sublist in abstract_suggest_result for item in sublist.abstract_suggest_list]

        suggest_index = {}
        for index, item in enumerate(abstract_suggest_list):
            suggest_index[index] = item.pop("citation_list")
            item["suggest_index"] = index

        if len(f"{abstract_suggest_list}") > DEFAULT_ABSTRACT_CHUNK_SIZE:
            abstract_suggest_chunk_list = self.suggest_chunking.chunk(abstract_suggest_list)

            runners = [AbstractGenerator(
                user_prompt=self.user_prompt,
                abstract_suggest_list=abstract_suggest,
                token_consumption=self.token_consumption
            ) for abstract_suggest in abstract_suggest_chunk_list]

            abstract_suggest_list = await self.run_with_concurrency(runners)
            generate_attach_response = await AbstractCombine(
                user_prompt=self.user_prompt,
                abstract_list=abstract_suggest_list,
                token_consumption=self.token_consumption
            ).run()

        else:
            generate_attach_response = await AbstractGenerator(user_prompt=self.user_prompt,
                                                               abstract_suggest_list=abstract_suggest_list,
                                                               token_consumption=self.token_consumption).run()

        suggest_format_fix_response = await SuggestFormatFix(content=generate_attach_response,
                                                             token_consumption=self.token_consumption).run()

        suggest_list = self.extract_suggest_numbers(text=suggest_format_fix_response)
        for suggest_id in suggest_list:
            if suggest_id in suggest_index:
                citation_str = "".join([f"[citation:{index}]" for index in suggest_index[suggest_id]])
                suggest_format_fix_response = suggest_format_fix_response.replace(f"[suggest:{suggest_id}]", citation_str)
            else:
                suggest_format_fix_response = suggest_format_fix_response.replace(f"[suggest:{suggest_id}]", "")

        return suggest_format_fix_response


if __name__ == '__main__':
    import asyncio
    from controller.analytics.summarizer_test import Summarizer

    stock_datas = Summarizer.test_stock_prompt_data_sync(stock_code="300750")
    doc_items = [DocModel(
        content=stock_data['content'],
        title=stock_data['title'],
        data_time=stock_data['data_time'][:10],
        extract_result=ExtractModel(**stock_data.get('extract_result')) if stock_data.get('extract_result') else None,
    ) for stock_data in stock_datas[:500]]
    workflow = AbstractWorkflow(
        doc_list=doc_items,
        user_prompt="我需要借助你的能力为股票进行评分，请充分参考宏观的世界经济形式和技术面，给出0-100的分值，并提供简单的分析说明。其中80分及以上代表推荐购买，60分以下不考虑。"
    )
    res = asyncio.run(workflow.run())
    print(res)
