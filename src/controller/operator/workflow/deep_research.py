from datetime import datetime
from enum import StrEnum
from typing import AsyncIterable, Sequence, Optional, Annotated

from autogen_agentchat.base import Response
from autogen_agentchat.messages import TextMessage, BaseChatMessage, ToolCallExecutionEvent, ToolCallRequestEvent
from autogen_core import CancellationToken
from autogen_core.models import Chat<PERSON><PERSON>pletionClient
from pydantic import BaseModel, Field

from common.logger import logger
from controller.engine import deepseek_chat, gemini_2_5_pro
from controller.operator.agent.deep_research.coordinator import CoordinatorAgent
from controller.operator.workflow import BaseWorkflow


class DeepResearchStage(StrEnum):
    ANSWER = "answer"
    PLANNING = "planning"
    THINKING = "thinking"
    REPORTING = "reporting"
    ERROR = "error"


class DeepResearchResponse(BaseModel):
    stage: Annotated[DeepResearchStage, Field(title="思考阶段")]
    create_time: Annotated[datetime, Field(title="创建时间", default_factory=datetime.now)] = None
    content: Annotated[Optional[str], Field(title="回复内容")] = None
    error_msg: Annotated[Optional[str], Field(title="错误信息")] = None


class DeepResearchWorkflow(BaseWorkflow):
    def __init__(self, user_prompt: str, use_web_search: bool = True):
        super().__init__()

        self.user_prompt = user_prompt
        self.use_web_search = use_web_search

    async def _run(self) -> AsyncIterable:
        coordinator_agent = CoordinatorAgent(model_client=deepseek_chat)
        messages: Sequence[BaseChatMessage] = [TextMessage(source="user", content=self.user_prompt)]
        cancellation_token = CancellationToken()

        use_tool, response = False, None
        async for message in coordinator_agent.on_messages_stream(messages, cancellation_token):
            if isinstance(message, ToolCallExecutionEvent):
                use_tool = True
            elif isinstance(message, Response):
                response = message

        if not use_tool:
            logger.info("No tool used, returning response directly.")
            yield DeepResearchResponse(stage=DeepResearchStage.ANSWER, content=response.chat_message.content)
            return




if __name__ == '__main__':
    import asyncio

    async def main():
        workflow = DeepResearchWorkflow(user_prompt="特斯拉的今日股价是多少？")
        async for response in workflow.run_stream():
            print(response)

    asyncio.run(main())
