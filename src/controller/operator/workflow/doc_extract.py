import asyncio
from typing import List

from controller.repository import DocAnalysis
from controller.operator.chunking import DocModel, BaseChunking, SlidingWindowChunking, ExtractModel
from controller.operator.runner.base import BaseRunner
from controller.operator.runner.doc_extract import BaseExtract, SentimentAnalysis, CustomExtract, CombineAbstract
from controller.operator.workflow import BaseWorkflow


class DocExtractWorkflow(BaseWorkflow):
    def __init__(self, doc: DocModel, chunking: BaseChunking = SlidingWindowChunking(), user_prompt: str = None,
                 update_index: str = None, update_id: str = None):
        super().__init__()

        self.doc = doc
        self.chunking = chunking
        self.user_prompt = user_prompt

        self.update_index = update_index
        self.update_id = update_id

        assert doc.content, ValueError("文档内容为空")

    async def _run(self) -> dict:
        self.doc.chunk_children = self.chunking.chunk(content=self.doc.content).chunk_children

        if len(self.doc.chunk_children) == 1:
            content_text = f"文章标题：{self.doc.title}\n文章内容：{self.doc.content}"

            runners: List[BaseRunner] = [
                BaseExtract(content=content_text, token_consumption=self.token_consumption),
                SentimentAnalysis(content=content_text, token_consumption=self.token_consumption),
                CustomExtract(content=content_text, user_prompt=self.user_prompt,
                              token_consumption=self.token_consumption) if self.user_prompt else None]

            base_result, sentiment_result, custom_result = await self.run_with_concurrency(runners=runners)

            extract_result = ExtractModel(
                subject=base_result.subject,
                industry=base_result.industry,
                positive_view=base_result.positive_view,
                negative_view=base_result.negative_view,
                sentiment_score=sentiment_result.SF,
                keywords=base_result.keywords,
                abstract=base_result.abstract,
            )

            if self.user_prompt:
                extract_result.analysis = custom_result

        else:
            base_extract_runners = [
                BaseExtract(
                    content=f"文章标题：{self.doc.title}\n文章片段：{chunk.chunk_text}",
                    token_consumption=self.token_consumption
                )
                for chunk in self.doc.chunk_children
            ]
            sentiment_analysis_runners = [
                SentimentAnalysis(
                    content=f"文章标题：{self.doc.title}\n文章片段：{chunk.chunk_text}",
                    token_consumption=self.token_consumption
                )
                for chunk in self.doc.chunk_children
            ]
            custom_extract_runners = [
                CustomExtract(
                    content=f"文章标题：{self.doc.title}\n文章片段：{chunk.chunk_text}",
                    user_prompt=self.user_prompt,
                    token_consumption=self.token_consumption
                )
                for chunk in self.doc.chunk_children
            ] if self.user_prompt else []

            coroutines = [
                self.run_with_concurrency(base_extract_runners),
                self.run_with_concurrency(sentiment_analysis_runners),
                self.run_with_concurrency(custom_extract_runners)
            ]

            base_extract_results, sentiment_analysis_results, custom_extract_results = await asyncio.gather(*coroutines)

            extract_result = ExtractModel(
                subject=list({item for chunk in base_extract_results for item in chunk.subject}),
                industry=list({item for chunk in base_extract_results for item in chunk.industry}),
                positive_view=[item for chunk in base_extract_results for item in chunk.positive_view],
                negative_view=[item for chunk in base_extract_results for item in chunk.negative_view],
                sentiment_score=sum(chunk.SF for chunk in sentiment_analysis_results) / len(sentiment_analysis_results),
                keywords=list({item for chunk in base_extract_results for item in chunk.tags}),
            )

            runners = [
                CombineAbstract(
                    content_list=[chunk.abstract for chunk in base_extract_results],
                    token_consumption=self.token_consumption
                ),
                CombineAbstract(
                    content_list=[custom_extract for custom_extract in custom_extract_results],
                    token_consumption=self.token_consumption
                ) if self.user_prompt else None
            ]
            combine_abstract, custom_abstract = await self.run_with_concurrency(runners)

            extract_result.abstract = combine_abstract
            if self.user_prompt:
                extract_result.analysis = custom_abstract

        if self.update_index and self.update_id:
            await DocAnalysis.update(
                index=self.update_index,
                _id=self.update_id,
                doc={"extract_result": extract_result.model_dump()}
            )

        return extract_result.model_dump()


if __name__ == '__main__':
    import json

    from controller.operator.example_text import short_text

    doc_item = DocModel(content=short_text)
    res = asyncio.run(DocExtractWorkflow(doc=doc_item).run())
    print(json.dumps(res, indent=4, ensure_ascii=False))
