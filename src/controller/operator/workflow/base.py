import asyncio
import time
import traceback
from abc import abstractmethod
from typing import List, Any

from common.logger import logger
from common.snowflake import Snow<PERSON><PERSON>
from controller.operator.runner.base import Runner<PERSON><PERSON><PERSON>, BaseR<PERSON>ner, TokenConsumption


WorkflowStatus = RunnerStatus


class BaseWorkflow:
    def __init__(self, concurrency: int = 20):
        self.id = Snowflake.get_id()
        self.status = WorkflowStatus.READY

        self.error_msg = None
        self.response = None

        self.concurrency = concurrency
        self.semaphore = asyncio.Semaphore(concurrency)
        self.token_consumption = TokenConsumption()

    async def run(self):
        task = asyncio.current_task()
        start_time = time.time()
        logger.info(f"Workflow: {self.__class__.__name__} {task.get_name()}:id:{self.id} is running.")
        self.status = WorkflowStatus.RUNNING

        try:
            result = await self._run()
            self.status = WorkflowStatus.SUCCESS
            self.response = result
            logger.info(f"Workflow: {self.__class__.__name__} {task.get_name()}:id:{self.id} is success after {time.time() - start_time:.2f} seconds. "
                         f"Input tokens[total]: {sum(self.token_consumption.input_token)}, Output tokens[total]: {sum(self.token_consumption.output_token)}.")
            return result
        except Exception as e:
            self.error_msg = ''.join(traceback.format_exception(type(e), value=e, tb=e.__traceback__))

        self.status = WorkflowStatus.FAILED
        logger.error(f"Workflow: {self.__class__.__name__} {task.get_name()}:id:{self.id} is failed. "
                      f"Error: {self.error_msg}")
        return None

    async def run_stream(self):
        async for response in self._run():
            yield response

    @abstractmethod
    async def _run(self, *args, **kwargs) -> Any:
        return "Success"

    async def run_with_concurrency(self, runners: List[BaseRunner]):
        async def _run_with_concurrency(runner: BaseRunner):

            async with self.semaphore:
                res = await runner.run()
                logger.debug(f"Workflow: {self.__class__.__name__} Runner: {runner.id} is done.")
                assert runner.status == RunnerStatus.SUCCESS, RuntimeError(f"Runner: {runner.id} failed.")

                return res

        tasks = [_run_with_concurrency(runner) for runner in runners]
        return await asyncio.gather(*tasks, return_exceptions=True)
