from pydantic import BaseModel, <PERSON>
from typing import Annotated, List, Optional

from config import LLMModel
from controller.engine import ChatEngine
from controller.operator.prompt.doc_extract import base_extract_prompt
from controller.operator.runner.base import BaseRunner, TokenConsumption
from controller.operator.tool import JsonifyAgent


class BaseExtractModel(BaseModel):
    subject: Annotated[List[str], Field(title="主体")]
    industry: Annotated[List[str], Field(title="所属行业")]
    positive_view: Annotated[List[str], Field(title="正面观点")]
    negative_view: Annotated[List[str], Field(title="负面观点")]
    keywords: Annotated[List[str], Field(title="关键词")]
    abstract: Annotated[str, Field(title="摘要")]
    extra: Annotated[Optional[str], Field(title="保留字段，无需提取")] = None


class BaseExtract(BaseRunner):
    def __init__(self, content: str, model_name: LLMModel = LLMModel.DEEPSEEK_CHAT,
                 token_consumption: TokenConsumption | None = None):
        super().__init__()

        self.content = content
        self.token_consumption = token_consumption or TokenConsumption()
        self.model_engine = ChatEngine(model_name=model_name, token_consumption=self.token_consumption)

    async def _run(self) -> BaseExtractModel:
        thinking_response = await self.model_engine.generator(
            system_prompt=base_extract_prompt,
            prompt=f"{self.content}",
        )
        thinking_content = thinking_response.choices[0].message.content.strip()

        jsonify_content = self.get_markdown_content(content=thinking_content)
        jsonify_agent = JsonifyAgent(BaseExtractModel, token_consumption=self.token_consumption)
        model: BaseExtractModel = await jsonify_agent.jsonify(content=jsonify_content)
        model.extra = thinking_content
        return model


if __name__ == '__main__':
    import asyncio

    async def main():
        from controller.operator.example_text import short_text

        results = await asyncio.gather(*[
            BaseExtract(content=short_text, model_name=LLMModel.QWEN3_32B).run()
            for _ in range(1)
        ])
        for model in results:
            print("--" * 20)
            print(model.extra)

    asyncio.run(main())
