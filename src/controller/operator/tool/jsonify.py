from typing import Type, Literal, Sequence, Any, Annotated

from pydantic import BaseModel, Field

from autogen_agentchat.messages import TextMessage, BaseChatMessage
from autogen_core import CancellationToken
from autogen_core.models import ChatCompletionClient

from controller.engine import gpt_41, gemini_2_5_flash, deepseek_chat
from controller.operator.agent.base import BaseAgent
from controller.operator.runner.base import TokenConsumption


class JsonifyAgent(BaseAgent):
    def __init__(self, jsonify_model: Type[BaseModel], model_client: ChatCompletionClient = gpt_41,
                 token_consumption: TokenConsumption | None = None):
        self.jsonify_model = jsonify_model
        self.token_consumption = token_consumption or TokenConsumption()

        if model_client.model_info.get("structured_output", False):
            super().__init__(
                name="jsonify_agent",
                description="一个 JSON 格式化助手，能够将用户输入的文本转换为指定的 JSON 格式，并提供格式化后的结果。",
                system_message="你是一个 JSON 格式化助手，能够将用户输入的文本转换为指定的 JSON 格式，并提供格式化后的结果。如果用户输入的文本无法转换为指定的 JSON 格式，请直接返回一个空值。",
                model_client=model_client,
                output_content_type=jsonify_model,
                token_consumption=self.token_consumption
            )
        else:
            super().__init__(
                name="jsonify_agent",
                description="一个 JSON 格式化助手，能够将用户输入的文本转换为指定的 JSON 格式，并提供格式化后的结果。",
                system_message=(
                    f"你是一个 JSON 格式化助手，能够将用户输入的文本转换为指定的 JSON 格式，并提供格式化后的结果。"
                    f"如果用户输入的文本无法转换为指定的 JSON 格式，请直接返回一个空值。"
                    f"请确保输出的 JSON 格式正确，并且包含所有必要的字段。你返回的 JSON 格式应该符合以下模型：{jsonify_model.model_json_schema()}。"
                    f"你返回的 JSON 格式应该以 ```json 开头，并以 ``` 结束。"),
                model_client=model_client,
                token_consumption=self.token_consumption
            )

    async def jsonify(self, content: str) -> Any:
        messages: Sequence[BaseChatMessage] = [
            TextMessage(source="user", content=content),
        ]
        cancellation_token = CancellationToken()
        response = await self.on_messages(messages, cancellation_token)
        content = response.chat_message.model_dump().get("content")

        if isinstance(content, str):
            if content.startswith("```json") and content.endswith("```"):
                content = content[7:-3].strip()
            content = self.jsonify_model.model_validate_json(content)
        elif isinstance(content, dict):
            content = self.jsonify_model.model_validate(content)
        else:
            raise TypeError(f"Unsupported jsonify content type: {type(content)}")

        return content


if __name__ == '__main__':
    import asyncio

    class IsHappy(BaseModel):
        thinking: Annotated[str, Field(description="思考过程")]
        is_happy: Annotated[bool, Field(description="是否开心")]
        reason_type: Annotated[Literal["主观原因", "客观原因"], Field(description="原因类型")]

    agent = JsonifyAgent(jsonify_model=IsHappy)
    res: IsHappy = asyncio.run(agent.jsonify(content="我很开心，因为今天阳光明媚。"))
    print(res)
