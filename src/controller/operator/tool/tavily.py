from typing import Optional, Annotated, List

import httpx
from pydantic import BaseModel, Field

from config import TAVILY_API_URL, TAVILY_API_KEY
from common.logger import logger


class TavilySearchResult(BaseModel):
    title: Annotated[str, Field(title="标题", description="搜索结果的标题")]
    url: Annotated[str, Field(title="URL", description="搜索结果的链接")]
    content: Annotated[Optional[str], Field(title="原始内容", description="搜索结果的原始内容")]
    score: Annotated[float, Field(title="相关性评分", description="搜索结果的相关性评分")]


class TavilySearchResponse(BaseModel):
    answer: Annotated[str, Field(title="搜索答案", description="Tavily搜索API返回的答案")]
    results: Annotated[List[TavilySearchResult], Field(title="搜索结果", description="Tavily搜索API返回的结果列表")]


async def tavily_search(query: str, max_results: Optional[int] = 5) -> TavilySearchResponse:
    """
    使用Tavily进行互联网搜索，获取最新的实时信息和相关内容。

    该工具可以帮助你获取最新的新闻、市场数据、公司信息、行业动态等网络信息。非常适合需要实时数据或最新信息的查询。

    Args:
        query: 搜索查询内容，应该是明确、具体的问题或关键词。例如"特斯拉最新季度财报"、"上海房价趋势"等
        max_results: 返回的最大结果数量，默认为5条。数量越多信息越全面，但处理时间可能更长

    Returns:
        TavilySearchResponse: 包含搜索答案和结果列表的对象，每个结果包含标题、URL、内容摘要和相关性评分
    """

    params = {
        "api_key": TAVILY_API_KEY,
        "query": query,
        "max_results": max_results,
        "search_depth": "advanced",
        "include_domains": [],
        "exclude_domains": [],
        "include_answer": True,
        "include_raw_content": True,
        "include_images": False,
        "include_image_descriptions": False,
    }

    max_retries = 3
    retry_interval = 3
    attempt = 0
    last_exception = None

    while attempt < max_retries:
        try:
            attempt += 1
            async with httpx.AsyncClient(trust_env=True) as client:
                response = await client.post(url=TAVILY_API_URL, json=params, timeout=10)
                response.raise_for_status()
                return TavilySearchResponse.model_validate(response.json())
        except httpx.ConnectError as e:
            last_exception = e
            logger.warning(f"连接 Tavily API 失败 (尝试 {attempt}/{max_retries}): {e}")
        except httpx.HTTPStatusError as e:
            last_exception = e
            logger.warning(f"Tavily API 返回错误状态码 (尝试 {attempt}/{max_retries}): {e.response.status_code} - {e.response.text}")
        except Exception as e:
            last_exception = e
            logger.warning(f"请求 Tavily API 异常 (尝试 {attempt}/{max_retries}): {e}")

        if attempt < max_retries:
            logger.info(f"等待 {retry_interval} 秒后重试...")
            await asyncio.sleep(retry_interval)

    logger.error(f"请求 Tavily API 失败，已重试 {max_retries} 次: {last_exception}")
    raise last_exception


if __name__ == "__main__":
    import asyncio
    result = asyncio.run(tavily_search(query="特斯拉的股价"))
    print(result.model_dump_json(indent=4))
