#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime
import hashlib
from pathlib import Path
from io import BytesIO
from enum import StrEnum, IntEnum
from typing import Optional

import asyncio
from sqlalchemy import select, update, func
from pydantic import BaseModel, Field, model_validator

from config.const import RepositoryDir
from engine.taskiq_task import broker
from engine.es import es
from engine.file_system import minio_client, repository_bucket
from engine.rdb import g, query_order, paginator, fetch_one, fetch_all
from common.logger import logger
from common.time import now_datetime_str
from exception.custom_exception import NotFoundError, AlreadyExistsError
from model.doc import DocModel, ALL_REPO_INDEX, DocStatus, get_index


class DoclingSupportSuffix(StrEnum):
    """from docling.document_converter import InputFormat - 支持所有docling原生格式"""
    # 文档格式
    pdf = ".pdf"
    docx = ".docx"
    xlsx = ".xlsx"
    pptx = ".pptx"
    html = ".html"
    htm = ".htm"
    md = ".md"
    txt = ".txt"

    # 图片格式 (所有图像格式都映射到 InputFormat.IMAGE)
    jpg = ".jpg"
    jpeg = ".jpeg"
    png = ".png"
    bmp = ".bmp"
    tiff = ".tiff"
    tif = ".tif"

    # 数据格式
    csv = ".csv"

    # 其他文档格式
    asciidoc = ".adoc"
    asc = ".asc"

class LibreofficePreConvertSupportSuffix(StrEnum):
    # word类
    doc = ".doc"

    # ppt类
    ppt = ".ppt"

    # excel类
    xls = ".xls"

class SpecialSupportSuffix(StrEnum):
    # 其他文档
    txt = ".txt"


class SupportSuffix(StrEnum):
    """合并了所有支持的文件后缀的枚举类"""
    # DoclingSupportSuffix成员 - 文档格式
    pdf = DoclingSupportSuffix.pdf
    docx = DoclingSupportSuffix.docx
    xlsx = DoclingSupportSuffix.xlsx
    pptx = DoclingSupportSuffix.pptx
    html = DoclingSupportSuffix.html
    htm = DoclingSupportSuffix.htm
    md = DoclingSupportSuffix.md
    txt = DoclingSupportSuffix.txt
    csv = DoclingSupportSuffix.csv
    asciidoc = DoclingSupportSuffix.asciidoc
    asc = DoclingSupportSuffix.asc

    # DoclingSupportSuffix成员 - 图像格式
    jpg = DoclingSupportSuffix.jpg
    jpeg = DoclingSupportSuffix.jpeg
    png = DoclingSupportSuffix.png
    bmp = DoclingSupportSuffix.bmp
    tiff = DoclingSupportSuffix.tiff
    tif = DoclingSupportSuffix.tif

    # LibreofficePreConvertSupportSuffix成员 (需要预转换)
    doc = LibreofficePreConvertSupportSuffix.doc
    ppt = LibreofficePreConvertSupportSuffix.ppt
    xls = LibreofficePreConvertSupportSuffix.xls


class TextDoc(BaseModel):
    doc_id: Optional[int] = Field(title="RDB文档ID", default=None)
    data_time: str = Field(title="数据时间")
    repo_id: int = Field(title="知识库ID")
    doc_type: str = Field(title="文档类型", default="text")
    title: str = Field(title="文档标题")
    content: str = Field("文档正文")
    source: Optional[str] = Field(title="文档来源", default=None)
    url: Optional[str] = Field(title="文档链接", default=None)
    author: Optional[str] = Field(title="文档作者", default=None)
    tags: Optional[list[str]] = Field(title="原文标签", default=[])

    filename: Optional[str] = Field(title="文件名称", default=None)  # 后续必须补充
    md5: Optional[str] = Field(title="文本唯一编码", default=None)

    @model_validator(mode="after")
    def check_total(self):
        self.title = self.title.strip()
        self.content = self.content.strip()

        if not self.md5:
            self.md5 = hashlib.sha256(self.content.encode("utf-8")).hexdigest()

        if not self.filename:
            self.filename = self.title

        return self


class DocController:
    @staticmethod
    def get_query(repo_id: int = None, doc_id: int = None, md5: str = None, match: str = None, order_by: str = None):
        where = [DocModel.is_delete == 0]
        if repo_id is not None:
            where.append(DocModel.repo_id == repo_id)
        if doc_id is not None:
            where.append(DocModel.id == doc_id)
        if md5:  # md5必须有值才可比较
            where.append(DocModel.md5 == md5)
        if match:
            where.append(DocModel.name.ilike(f"%{match}%"))

        query = (
            select(
                DocModel.id.label("doc_id"),
                DocModel.repo_id,
                DocModel.name,
                DocModel.path,
                DocModel.tags,
                DocModel.size,
                DocModel.status,
                DocModel.create_user_id,
                DocModel.create_time)
            .where(*where)
        )
        query = query_order(query=query, order_by=order_by, table=DocModel)

        return query

    async def get_list(self, repo_id: int = None, match: str = None, page: int = None, per_page: int = None,
                       order_by: str = None):
        query = self.get_query(repo_id=repo_id, match=match, order_by=order_by)
        pager, docs = await paginator(query, page=page, per_page=per_page)

        return pager, docs

    async def get_one(self, doc_id: int = None, md5: str = None, repo_id: int = None):
        query = self.get_query(repo_id=repo_id, doc_id=doc_id, md5=md5)
        return await fetch_one(query)

    @staticmethod
    def get_es_query(repo_id: int = None, repo_ids: list[int] = None, doc_ids: list[int] = None,
                     match: str | list[str] = None, start_time: str | datetime.datetime = None,
                     end_time: str | datetime.datetime = None):
        filters = []
        if repo_id is not None:
            filters.append({"term": {"repo_id": repo_id}})
        if repo_ids is not None:
            filters.append({"terms": {"repo_id": repo_ids}})
        if doc_ids is not None:
            filters.append({"terms": {"_id": doc_ids}})
        if (start_time is not None) or (end_time is not None):
            range_time = {}
            if start_time:
                range_time["gte"] = str(start_time)
            if end_time:
                range_time["lt"] = str(end_time)
            filters.append({"range": {"data_time": range_time}})
        if match:
            if isinstance(match, str):
                match = [match]
            match_should = []
            for kw in match:
                match_should.append({
                    "query_string": {
                        "query": f"title:*{kw}* OR author:*{kw}* OR content:*{kw}* OR tags:*{kw}*",
                        "analyze_wildcard": True
                    }
                })
            filters.append({"bool": {"should": match_should}})

        query = {
            "bool": {
                "filter": filters,
            }
        }
        # print(json.dumps(query, indent=4, ensure_ascii=False))
        return query

    async def get_es_all(self, repo_ids: list[int] = None, doc_ids: list[int] = None,
                         match: str | list[str] = None, start_time: str | datetime.datetime = None,
                         end_time: str | datetime.datetime = None, size: int = 10000, order_by: str = None,
                         includes: list[str] = None):
        index = get_index(repo_ids=repo_ids)
        query = self.get_es_query(repo_ids=repo_ids, doc_ids=doc_ids, start_time=start_time,
            end_time=end_time, match=match)

        if order_by:
            if isinstance(order_by, str):
                order_by = [{ob.split(":")[0]: ob.split(":")[1]} for ob in order_by.split(",")]
        else:
            order_by = [{"data_time": "desc"}, {"create_time": "asc"}]

        docs = []
        if size <= 10000:
            search_result = await es.search(
                index=index,
                query=query,
                size=size,
                _source_includes=includes,
                sort=order_by,
                track_total_hits=False)

            if len(search_result["hits"]["hits"]) > 0:
                docs = [doc["_source"] for doc in search_result["hits"]["hits"]]
        else:
            pit = await es.open_point_in_time(index=index, keep_alive="1m")
            pit_id = pit["id"]

            search_after = None

            while True:
                search_result = await es.search(
                    query=query,
                    size=10000,
                    _source_includes=includes,
                    sort=order_by,
                    pit={"id": pit_id, "keep_alive": "1m"},
                    search_after=search_after,
                    track_total_hits=False)

                if len(search_result["hits"]["hits"]) > 0:
                    search_after = search_result["hits"]["hits"][-1]["sort"]
                    pit_id = search_result["pit_id"]
                    source_data = [doc["_source"] for doc in search_result["hits"]["hits"]]
                    docs.extend(source_data)
                    if len(docs) >= size:
                        docs = docs[:size]
                        break
                    continue

                await es.close_point_in_time(body={"id": pit_id})
                break

        return docs

    async def get_es_list(self, repo_id: int = None, match: str = None, page: int = None, per_page: int = None):
        query = self.get_es_query(repo_id=repo_id, match=match)
        per_page = per_page or 20
        res = await es.search(
            index=ALL_REPO_INDEX,
            query=query,
            sort=[{"data_time": "desc"}, {"create_time": "asc"}],
            from_=(page - 1) * per_page if page else None,
            size=per_page or 20 if page else None,
            source_includes=["title", "data_time", "tags", "extract_result.sentiment_score", "extract_result.keywords"])

        hits = res["hits"]["hits"]
        docs = []
        for hit in hits:
            doc = {
                "doc_id": hit["_id"],
                **hit["_source"]
            }
            # todo:临时方法
            doc.setdefault("tags", [])
            doc.setdefault("extract_result", {})
            doc["extract_result"].setdefault("sentiment_score", None)
            doc["extract_result"].setdefault("keywords", None)
            docs.append(doc)

        return docs

    @staticmethod
    async def get_es_one(repo_id: int, doc_id: int, includes: list[str] = None, excludes: list[str] = None):
        index = get_index(repo_ids=[repo_id])

        doc = await es.get(
            index=index,
            id=doc_id,
            source_includes=includes,
            source_excludes=excludes)
        if not doc:
            raise NotFoundError(message="未找到指定ES文档")

        _source = doc["_source"]
        return _source

    @staticmethod
    async def get_es_chunks(citation_ids: list[str]):
        res = await es.search(
            index=ALL_REPO_INDEX,
            query={
                "nested": {
                    "path": "chunks",
                    "query": {
                        "terms": {"chunks.cid": citation_ids}
                    },
                    "inner_hits": {
                        "_source": ["chunks.cid", "chunks.html_content", "chunks.xpath"]
                    }
                }
            },
            source_includes=["doc_id", "repo_id"],
            size=10000
            )

        chunks = [{**hit["_source"], **chunk["_source"]} for hit in res["hits"]["hits"] for chunk in hit["inner_hits"]["chunks"]["hits"]["hits"]]

        return chunks

    @staticmethod
    async def get_object(filepath: Path):
        def _get_object():
            return minio_client.get_object(
                bucket_name=repository_bucket,
                object_name=filepath.as_posix()
            )

        with await asyncio.to_thread(_get_object) as response:
            return response.read().decode("utf-8")

    async def create(self, repo_id: int, name: str, size: int, md5: str, status: DocStatus, tags: list[str] = None,
                     info: dict = None, create_time: str = None, repetition_strategy: str = "raise"):
        if doc := await self.get_one(repo_id=repo_id, md5=md5):
            if repetition_strategy == "return":
                logger.warning(f"Doc[{repo_id}]: 文档已存在,根据重复策略返回存在id: [{doc['doc_id']}]")
                return doc["doc_id"]
            else:
                raise AlreadyExistsError(message="文档已存在")

        tags = tags or []
        info = info or {}
        create_time = create_time or now_datetime_str()

        doc = DocModel(
            repo_id=repo_id, name=name, path="", size=size, md5=md5, status=status, tags=tags, info=info,
            create_time=create_time)
        g.session.add(doc)
        await g.session.flush()

        return doc.id

    @staticmethod
    async def update(doc_id: int, md5: str = None, status: DocStatus = None, path: Path = None,
                     tags: list[str] = None):
        update_info = {}
        if md5 is not None:
            update_info[DocModel.md5] = md5
        if status is not None:
            update_info[DocModel.status] = status
        if path is not None:
            update_info[DocModel.path] = path.as_posix()
        if tags is not None:
            update_info[DocModel.tags] = tags

        query = (update(DocModel)
                 .where(DocModel.id == doc_id)
                 .values(update_info))
        await g.session.execute(query)

    @staticmethod
    async def delete(repo_id: int, doc_id: str):
        query = (update(DocModel)
                 .where(DocModel.id == doc_id)
                 .values({DocModel.is_delete: 1}))
        await g.session.execute(query)

        index = get_index(repo_ids=[repo_id])
        await es.delete(index=index, id=doc_id)

    @staticmethod
    async def stats_repo_doc_counts():
        query = (
            select(
                DocModel.repo_id,
                func.count(DocModel.id).label("doc_count"))
            .where(DocModel.is_delete == 0)
            .group_by(DocModel.repo_id)
        )
        return await fetch_all(query)

    @staticmethod
    def get_path(repo_id: int, doc_id: int, filename: str):
        return RepositoryDir / str(repo_id) / str(doc_id) / filename

    @staticmethod
    async def upload_s3(path: Path, content: bytes):
        def _upload():
            minio_client.put_object(
                bucket_name=repository_bucket,
                object_name=path.as_posix(),
                data=BytesIO(content),
                length=len(content)
            )

        await asyncio.to_thread(_upload)

    async def send_doc_parsing_task(self, doc_id: int):
        await self.doc_parsing.kiq(doc_id=doc_id)
        logger.info(f"Doc[{doc_id}]: 文档解析任务已发送")

    # tasks.doc_parsing的占位方法
    @staticmethod
    @broker.task(task_name="doc_parsing")
    async def doc_parsing(doc_id: int): ...


Doc = DocController()
