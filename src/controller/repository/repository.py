#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import inspect

from pydantic import BaseModel, Field
from sqlalchemy import select, update, or_, and_, func

from engine.rdb import session_maker_sync, paginator, query_order, fetch_all, fetch_one, g
from engine.es import es, es_sync
from exception.custom_exception import ApiError
from model.repository import RepositoryModel, RepositoryType
from model.doc import DocModel
from model.doc import DOC_MAPPING, get_index
from common.logger import logger


class RepositoryConfig(BaseModel):
    """知识库AI抽取配置"""
    abstract: bool = Field(title="是否抽取摘要", default=True)
    subject: bool = Field(title="是否抽取主体", default=True)
    sentiment_score: bool = Field(title="是否抽取情绪因子", default=True)
    keywords: bool = Field(title="是否抽取关键信息", default=True)
    views: bool = Field(title="是否抽取观点", default=True)


class DefaultRepoSchema(BaseModel):
    id: int
    name: str
    doc_tags: list[str] = None
    type_: RepositoryType = RepositoryType.public


class DefaultRepoData:
    STOCK_NEWS = DefaultRepoSchema(id=-1, name="东方财富-个股新闻", doc_tags=["个股新闻"])
    STOCK_NEWS_MAIN_CX = DefaultRepoSchema(id=-2, name="财新网-财经内容精选", doc_tags=["财经新闻"])
    STOCK_INFO_GLOBAL_THS = DefaultRepoSchema(id=-3, name="同花顺财经-全球财经直播", doc_tags=["财经新闻"])
    STOCK_KLINE_INDICATOR = DefaultRepoSchema(id=-4, name="个股-日线和分析指标", doc_tags=["日线", "分析指标"])
    STOCK_VALUE_FLOW = DefaultRepoSchema(id=-5, name="东方财富-个股估值和资金流向", doc_tags=["估值", "资金流向"])
    STOCK_INSTITUTE_HOLD = DefaultRepoSchema(id=-6, name="新浪财经-机构持股", doc_tags=["机构持股"])
    STOCK_COMPANY_NEWS = DefaultRepoSchema(id=-7, name="东方财富-公司动态", doc_tags=["公司动态"])
    STOCK_PERFORMANCE_REPORT = DefaultRepoSchema(id=-8, name="东方财富-业绩快报", doc_tags=["业绩"])
    STOCK_PERFORMANCE_FORECAST = DefaultRepoSchema(id=-9, name="东方财富-业绩预告", doc_tags=["业绩"])
    STOCK_ANALYST_RANK = DefaultRepoSchema(id=-10, name="东方财富-分析师评级", doc_tags=["分析师", "评级"])
    STOCK_SCORE = DefaultRepoSchema(id=-11, name="东方财富-千股千评", doc_tags=["评级"])
    STOCK_RESEARCH_REPORT = DefaultRepoSchema(id=-12, name="个股-机构研报", doc_tags=["研报"])



class RepositoryController:
    @staticmethod
    def get_query(repo_id: int = None, repo_ids: list[int] = None, type_: RepositoryType = None,
                  match: str = None, user_id: int = None, create_user_id: int = None, order_by: str = None):
        where = [RepositoryModel.is_delete == 0]
        if repo_id is not None:
            where.append(RepositoryModel.id == repo_id)
        if repo_ids is not None:
            where.append(RepositoryModel.id.in_(repo_ids))
        if match:
            where.append(RepositoryModel.name.ilike(f"%{match}%"))
        if user_id:
            where.append(
                or_(
                    RepositoryModel.type_ == RepositoryType.public,
                    and_(RepositoryModel.type_ == RepositoryType.private,
                         RepositoryModel.create_user_id == user_id)
                )
            )
        if create_user_id is not None:
            where.append(RepositoryModel.create_user_id == create_user_id)
        if type_:
            where.append(RepositoryModel.type_ == type_)

        # 使用子查询来计算文档数量，避免GROUP BY的复杂性
        doc_count_subquery = (
            select(func.count(DocModel.id))
            .where(DocModel.repo_id == RepositoryModel.id)
        ).scalar_subquery()

        query = (
            select(
                RepositoryModel.id.label("repo_id"),
                RepositoryModel.name,
                RepositoryModel.type_,
                RepositoryModel.config,
                RepositoryModel.create_time,
                func.coalesce(doc_count_subquery, 0).label("doc_count"))
            .where(*where))

        query = query_order(query=query, order_by=order_by, table=RepositoryModel)

        return query

    async def get_one(self, repo_id: int, user_id: int = None, create_user_id: int = None):
        query = self.get_query(repo_id=repo_id, user_id=user_id, create_user_id=create_user_id)
        return await fetch_one(query=query)

    async def get_list(self, repo_id: int = None, type_: RepositoryType = None, match: str = None, user_id: int = None,
                       page: int = None, per_page: int = None, order_by: str = None):
        query = self.get_query(
            repo_id=repo_id, type_=type_, match=match, user_id=user_id, order_by=order_by)
        return await paginator(query=query, page=page, per_page=per_page)

    async def get_all(self, repo_id: int = None, repo_ids: list[int] = None, type_: str = None, match: str = None,
                      user_id: int = None, create_user_id: int = None, order_by: str = None):
        query = self.get_query(
            repo_id=repo_id, repo_ids=repo_ids, type_=type_, match=match, user_id=user_id, create_user_id=create_user_id,
            order_by=order_by)
        return await fetch_all(query=query)

    async def get_id_name_mapping(self, repo_ids: list[int]):
        query = self.get_query(repo_ids=repo_ids)
        repos = await fetch_all(query)
        return {repo["repo_id"]: repo["name"] for repo in repos}

    async def create(self, name: str, type_: RepositoryType, config: RepositoryConfig):
        repo = RepositoryModel(name=name, type_=type_, config=config.model_dump())
        g.session.add(repo)
        await g.session.flush()

        repo_id = repo.id

        await self.create_es_index(repo_id=repo_id)

        return repo.id

    def init_sync(self):
        with session_maker_sync() as session:
            for key,repo in inspect.getmembers(DefaultRepoData):
                if (
                        not key.startswith("__")  # 排除魔术方法
                        and not inspect.isroutine(repo)  # 排除普通方法
                        and not inspect.isdatadescriptor(repo)  # 排除属性装饰器
                ):
                    session.add(
                        RepositoryModel(
                            id=repo.id, name=repo.name, type_=repo.type_, config=RepositoryConfig().model_dump()))
                    self.init_es_index_sync(repo_id=repo.id)
                session.commit()

    @staticmethod
    async def update(repo_id: int, name: str = None, config: RepositoryConfig = None, doc_count: int = None):
        update_info = {}
        if name is not None:
            update_info[RepositoryModel.name] = name
        if config is not None:
            update_info[RepositoryModel.config] = config.model_dump()
        if doc_count is not None:
            update_info[RepositoryModel.doc_count] = doc_count
        query = (update(RepositoryModel)
                 .where(RepositoryModel.id == repo_id)
                 .values(update_info))

        await g.session.execute(query)

    async def delete(self, repo_id: int):
        query = (update(RepositoryModel)
                 .where(RepositoryModel.id == repo_id)
                 .values({RepositoryModel.is_delete: 1}))
        await g.session.execute(query)

        await self.delete_docs(repo_id=repo_id)
        await self.delete_es_index(repo_id=repo_id)

    @staticmethod
    async def delete_docs(repo_id: int):
        query = (update(DocModel)
                 .where(DocModel.repo_id == repo_id)
                 .values({DocModel.is_delete: 1}))
        await g.session.execute(query)

    @staticmethod
    async def create_es_index(repo_id: int):
        index = get_index(repo_ids=[repo_id])

        if await es.indices.exists(index=index):
            raise ApiError(message=f"ES索引[{index}]已存在,请检查逻辑是否有误")
        try:
            res = await es.indices.create(index=index, body=DOC_MAPPING)
        except Exception as err:
            raise ApiError(f"ES索引创建失败: {str(err)}")
        else:
            if res["acknowledged"] is True:
                logger.info(f"ES索引 [{index}] 创建成功")
            else:
                raise ApiError(f"ES索引创建失败: {str(res)}")

    @staticmethod
    def init_es_index_sync(repo_id: int):
        index =get_index(repo_ids=[repo_id])

        if es_sync.indices.exists(index=index):
            return
        try:
            res = es_sync.indices.create(index=index, body=DOC_MAPPING)
        except Exception as err:
            raise ApiError(f"ES索引创建失败: {str(err)}")
        else:
            if res["acknowledged"] is True:
                logger.info(f"ES索引 [{index}] 创建成功")
            else:
                raise ApiError(f"ES索引创建失败: {str(res)}")

    @staticmethod
    async def delete_es_index(repo_id: int):
        index = get_index(repo_ids=[repo_id])

        try:
            res = await es.indices.delete(index=index)
        except Exception as err:
            raise ApiError(f"ES删除索引失败: {str(err)}")
        else:
            if res["acknowledged"] is True:
                logger.info(f"ES索引 [{index}] 删除成功")
            else:
                raise ApiError(f"ES索引删除失败: {str(res)}")


Repo = RepositoryController()
