from enum import StrEnum, Enum


class LLMModel(StrEnum):
    DEEPSEEK_CHAT = "deepseek-chat"
    DEEPSEEK_REASONER = "deepseek-reasoner"
    GPT_41 = "gpt-4.1"
    GPT_4O = "gpt-4o"
    O4_MINI = "o4-mini"
    QWEN3_30B = "qwen3:30b"
    QWEN3_32B = "qwen3-32b"
    QWEN3_235B = "qwen3-235b-a22b"
    CLAUDE_4_SONNET = "claude-sonnet-4-20250514"
    GEMINI_2_5_FLASH = "gemini-2.5-flash"
    GEMINI_2_5_PRO = "gemini-2.5-pro"

class EmbeddingModel(StrEnum):
    BGE_M3 = "bge-m3"
    TEXT_EMBEDDING_3_LARGE = "text-embedding-3-large"

class RerankerModel(StrEnum):
    BGE_RERANKER_V2_M3 = "bge-reranker-v2-m3"
