#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from sqlalchemy.orm import Mapped, mapped_column

from model.base import BaseModel


class StrategyModel(BaseModel):
    __tablename__ = "strategy"
    __comment__ = "策略表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="配置ID")
    config: Mapped[dict] = mapped_column(nullable=False, default='{}', comment="系统配置")
