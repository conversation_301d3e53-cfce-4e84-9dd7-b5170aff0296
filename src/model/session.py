import uuid
from enum import Enum
from typing import List, Annotated, Optional

from pydantic import BaseModel, Field

from common.time import now_tz_datestring_with_millis
from controller.operator.chunking import RetrieveChunkModel


class ChatHistory(BaseModel):
    request_id: Annotated[str, Field(title="请求ID")] = str(uuid.uuid4())
    user: Annotated[str, Field(title="用户输入内容")]
    thinking: Annotated[Optional[str], Field(title="模型思考内容")] = None
    assistant: Annotated[Optional[str], Field(title="模型回复内容")] = None
    is_delete: Annotated[bool, Field(title="是否软删除")] = False
    query_time: Annotated[str, Field(title="提问时间")] = now_tz_datestring_with_millis()
    error_msg: Annotated[Optional[str], Field(title="错误信息")] = None
    reference: Annotated[Optional[List[RetrieveChunkModel]], Field(title="参考资料")] = None


class ChatSessionType(Enum):
    CHAT = "chat"
    QA_DOC = "qa_doc"
    IMAGE = "image"


class ChatSession(BaseModel):
    session_id: Annotated[str, Field(title="会话ID")]
    session_type: Annotated[ChatSessionType, Field(title="会话类型")]
    create_time: Annotated[str, Field(title="创建时间")]
    is_delete: Annotated[bool, Field(title="是否软删除")] = False
    chat_history: Annotated[List[ChatHistory], Field(title="聊天记录")] = List

    class Config:
        use_enum_values = True


CHAT_SESSION_INDEX = "chat_session"

CHAT_SESSION_MAPPING = {
    "settings": {
        "number_of_shards": 1,
        "number_of_replicas": 0,
        "analysis": {
            "analyzer": {
                "ngram_verbatim": {
                    "tokenizer": "ngram_verbatim_tokenizer"
                }
            },
            "tokenizer": {
                "ngram_verbatim_tokenizer": {
                    "type": "ngram",
                    "min_gram": 1,
                    "max_gram": 1,
                    "token_chars": []
                }
            }
        }
    },
    "mappings": {
        "dynamic": False,
        "properties": {
            # 会话ID
            "session_id": {
                "type": "keyword"
            },
            # 会话类型
            "session_type": {
                "type": "keyword"
            },
            # 创建时间
            "create_time": {
                "type": "date",
                "format": "yyyy-MM-dd HH:mm:ss||strict_date_optional_time||epoch_millis"
            },
            # 是否软删除
            "is_delete": {
                "type": "boolean"
            },
            # 聊天记录
            "chat_history": {
                "type": "nested",
                "properties": {
                    # 请求ID
                    # 每轮对话衍生出新的request_id
                    "request_id": {
                        "type": "keyword"
                    },
                    # 用户输入内容
                    "user": {
                        "type": "text",
                        "analyzer": "ngram_verbatim",
                        "fields": {
                            "ngram": {
                                "type": "text",
                                "analyzer": "ngram_verbatim",
                            },
                            "keyword": {
                                "type": "keyword"
                            }
                        }
                    },
                    # 模型回复内容
                    "assistant": {
                        "type": "text",
                        "analyzer": "ngram_verbatim",
                        "fields": {
                            "ngram": {
                                "type": "text",
                                "analyzer": "ngram_verbatim",
                            }
                        }
                    },
                    # 模型思考内容
                    "thinking": {
                        "type": "text",
                        "analyzer": "ngram_verbatim",
                        "fields": {
                            "ngram": {
                                "type": "text",
                                "analyzer": "ngram_verbatim",
                            }
                        }
                    },
                    # 是否软删除
                    "is_delete": {
                        "type": "boolean"
                    },
                    # 提问时间
                    "query_time": {
                        "type": "date",
                        "format": "yyyy-MM-dd HH:mm:ss.SSSSSS||yyyy-MM-dd HH:mm:ss||strict_date_optional_time||epoch_millis"
                    },
                    # 错误信息
                    "error_msg": {
                        "type": "text",
                        "analyzer": "ngram_verbatim",
                        "fields": {
                            "ngram": {
                                "type": "text",
                                "analyzer": "ngram_verbatim",
                            }
                        }
                    },
                    # 溯源信息
                    "reference": {
                        "type": "flattened"
                    }
                }
            }
        }
    }
}
