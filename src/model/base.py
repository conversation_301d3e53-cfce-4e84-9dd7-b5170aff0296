import json
import decimal
from typing import Any
from datetime import date, time, timedelta, datetime
from json import JSONEncoder

from dataclasses import is_dataclass, asdict
from sqlalchemy import Integer, TypeDecorator, SmallInteger, JSON
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy.dialects.mysql import LONGTEXT, DATETIME

from common import g
from common.time import now_datetime


class CustomJSONEncoder(JSONEncoder):
    """修改datetime打印格式"""
    def default(self, obj):
        try:
            if isinstance(obj, (date, time, timedelta)):
                return str(obj)[:19]
            if isinstance(obj, decimal.Decimal):
                return str(obj)
            if is_dataclass(obj):
                return asdict(obj)
            iterable = iter(obj)
        except TypeError as err:
            print(err)
        else:
            return list(iterable)
        return JSONEncoder.default(self, obj)


class JsonText(TypeDecorator):
    impl = LONGTEXT  # 基础类型
    cache_ok = True  # 是否使用Sqlalchemy缓存

    def process_bind_param(self, value: (dict, list), dialect):
        if value is not None:
            value = json.dumps(value, ensure_ascii=False, cls=CustomJSONEncoder)
        return value

    def process_result_value(self, value: str, dialect):
        if value is not None:
            value = json.loads(value)
        return value


class Boolean(TypeDecorator):
    impl = SmallInteger  # 基础类型
    cache_ok = True  # 是否使用Sqlalchemy缓存

    def process_bind_param(self, value: (dict, list), dialect):
        if value is not None:
            value = int(value)
        return value

    def process_result_value(self, value: str, dialect):
        if value is not None:
            value = bool(value)
        return value


class LongText(TypeDecorator):
    impl = LONGTEXT  # 基础类型
    cache_ok = True  # 是否使用Sqlalchemy缓存


def get_attr_from_g(name: str, default: Any = None, raise_exception: bool = False):
    """从g对象中获取默认参数"""

    def getter():
        try:
            if getattr(g, name, None) is None:
                if raise_exception:
                    raise AttributeError("flask g has not attribute {name}")
                return default
            return getattr(g, name)
        except RuntimeError:
            return default

    return getter


class BaseModel(DeclarativeBase):
    """基类表模板"""
    __abstract__ = True
    type_annotation_map = {
        int: Integer,
        datetime: DATETIME(3).with_variant(DATETIME(fsp=3), "mysql"),
        bool: Boolean,
        dict: JSON,
        list: JSON
    }

    create_user_id: Mapped[int] = mapped_column(nullable=False, index=True, default=get_attr_from_g("user_id", default=0), comment="创建用户ID")
    create_time: Mapped[datetime] = mapped_column(nullable=False, default=now_datetime, index=True, comment="创建时间")
    update_user_id: Mapped[int] = mapped_column(nullable=False, default=get_attr_from_g("user_id", default=0), onupdate=get_attr_from_g("user_id", default=0), comment="修改用户ID")
    update_time: Mapped[datetime] = mapped_column(nullable=False, default=now_datetime, onupdate=now_datetime, comment="修改时间")
    is_delete: Mapped[bool] = mapped_column(nullable=False, default=False, comment="是否已删除")

    def to_dict(self, include: list = None, exclude: list = None):
        exclude = [] if exclude is not None else exclude
        include_mapper = {}
        if include is not None:
            for key in include:
                if ":" in key:
                    rename_key = key.split(":", maxsplit=1)
                    include_mapper.setdefault(*rename_key)
                else:
                    include_mapper[key] = key

        res = {}
        for c in self.__mapper__.column_attrs:
            if include is not None and c.key not in include_mapper:
                continue
            if exclude is not None and c.key in exclude:
                continue
            res[include_mapper.get(c.key, c.key)] = getattr(self, c.key, None)
        return res
