#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from datetime import datetime

from sqlalchemy import String
from sqlalchemy.orm import Mapped, mapped_column

from model.base import BaseModel


class UserModel(BaseModel):
    __tablename__ = "user"
    __comment__ = "用户表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="用户ID")
    username: Mapped[str] = mapped_column(String(32), nullable=False, comment="用户账号")
    password: Mapped[str] = mapped_column(String(256), nullable=False, comment="用户密码")
    nickname: Mapped[str] = mapped_column(String(12), nullable=False, comment="用户昵称")
    email: Mapped[str] = mapped_column(String(32), nullable=True, comment="用户邮箱")
    phone_number: Mapped[str] = mapped_column(String(16), nullable=True, comment="用户手机")
    expire_time: Mapped[datetime] = mapped_column(nullable=True, comment="到期时间")
    role_ids: Mapped[list] = mapped_column(nullable=False, comment="角色ID")
    info: Mapped[dict] = mapped_column(nullable=False, default='{}', comment="其他信息")