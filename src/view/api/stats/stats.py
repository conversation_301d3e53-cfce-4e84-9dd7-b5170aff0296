#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Body
from pydantic import Field

from view import BaseView, api_description
from controller.stats import Stats
from controller.stats.stats import StatsContent


class StatsView(BaseView):
    @api_description(summary="首页数据统计")
    async def post(self,
                   repo_ids: Annotated[list[int], Body(), Field(title="知识库ID")],
                   keywords: Annotated[list[str], Body(), Field(title="关键词")],
                   exclude_keywords: Annotated[list[str], Body(), Field("排除关键词")] = None,
                   start: Annotated[str, Body(), Field(title="开始时间")] = None,
                   end: Annotated[str, Body(), Field(title="结束时间")] = None):
        keywords = [kw.strip() for kw in keywords if kw.strip()]
        if exclude_keywords:
            exclude_keywords = [kw.strip() for kw in exclude_keywords if kw.strip()]

        # ES文档统计
        stats_contents = [
            StatsContent.doc_count_trend,
            StatsContent.sentiment_score_trend,
            StatsContent.sentiment_score_range,
            StatsContent.top_sentiment_docs,
            StatsContent.top_view_keywords,
            StatsContent.top_subject,
            StatsContent.top_doc_keywords,
            StatsContent.view_count_ratio_trend
        ]
        stats_result = await Stats.get_doc_stats(
            stats_contents=stats_contents,
            keywords=keywords,
            exclude_keywords=exclude_keywords,
            repo_ids=repo_ids,
            start=start,
            end=end)

        return self.response(data=stats_result)
