#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from view import BaseView, api_description
from controller.admin.user import role_mapping, AdminRole
from exception.custom_exception import PermissionDenyError


class RoleAllView(BaseView):
    @api_description(summary="查询所有角色")
    async def get(self):
        if AdminRole.role_id not in self.role_ids:
            raise PermissionDenyError()
        return self.response(data=list(role_mapping.values()))
