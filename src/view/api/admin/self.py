#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Body

from view import BaseView, api_description
from controller.admin import User, Auth
from common.context import g
from common.sm import gm_sm2, gm_sm4
from exception.custom_exception import ParamsCheckError


class ResetPasswordView(BaseView):
    @api_description(summary="重置密码")
    async def put(self,
                  password: Annotated[str, Body(title="新密码", embed=True)]):
        try:
            password_plaintext = gm_sm2.decrypt(password)
            password = gm_sm4.encrypt(password_plaintext)
        except Exception:
            raise ParamsCheckError(f"密码不合法")
        await User.update(user_id=g.user_id, password=password)
        await g.session.commit()

        await Auth.delete_token_cache(user_ids=[g.user.id])

        return self.response()


class UserSelfView(BaseView):
    @api_description(summary="查询自账户信息")
    async def get(self):
        self_user = await Auth.get_token_cache(user_id=g.user_id)
        return self.response(data=self_user)
