#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import hashlib
from typing import Annotated
from pathlib import Path

from fastapi import Query, Body, Form, File, UploadFile
from pydantic import Field

from config import PARSER_FILE_SIZE
from engine.rdb import g
from view import BaseView, api_description
from exception.custom_exception import ParamsCheckError, NotFoundError
from controller.repository import Repo, Doc, SupportSuffix, DocStatus


class DocListView(BaseView):
    @api_description(summary="查询文档列表")
    async def get(self,
                  repo_id: Annotated[int, Query(title="知识库ID")],
                  match: Annotated[str, Query(title="模糊匹配")] = None,
                  page: Annotated[int, Query(title="分页页数")] = None,
                  per_page: Annotated[int, Query(title="分页容量")] = None,
                  order_by: Annotated[str, Query(title="排序条件")] = "create_time:desc"):
        pager, docs = await Doc.get_list(repo_id=repo_id, match=match, page=page, per_page=per_page)

        doc_id_mapping = {doc["doc_id"]: doc for doc in docs}
        es_docs = await Doc.get_es_all(
            doc_ids=list(doc_id_mapping.keys()),
            includes=["doc_id", "keywords", "extract_result.sentiment_influence_score"])
        for es_doc in es_docs:
            if es_doc["doc_id"] in doc_id_mapping:
                if es_doc.get("extract_result") and es_doc["extract_result"].get("sentiment_influence_score"):
                    es_doc["sentiment_influence_score"] = es_doc["extract_result"]["sentiment_influence_score"]
                    del es_doc["extract_result"]
                doc_id_mapping[es_doc["doc_id"]].update(es_doc)

        return self.response(data=docs, pager=pager)

class DocView(BaseView):
    @api_description(summary="查询文档详情")
    async def get(self,
                  doc_id: Annotated[int, Query(title="文档ID")]):
        if self.is_admin:
            user_id = None
            create_user_id = None
        else:
            user_id = create_user_id = self.user_id

        if not (doc := await Doc.get_one(doc_id=doc_id)):
            raise NotFoundError("未找到目标数据")
        if not (repo := await Repo.get_one(repo_id=doc["repo_id"], user_id=user_id, create_user_id=create_user_id)):
            raise NotFoundError("未找到目标数据")


        es_doc = await Doc.get_es_one(
            repo_id=doc["repo_id"], doc_id=doc_id, excludes=["chunks", "title", "filename", "plain_text", *doc.keys()])
        doc.update(es_doc)

        return self.response(data=doc)

    @api_description(summary="删除文档")
    async def delete(self,
                     repo_id: Annotated[int, Body(title="知识库ID")],
                     doc_id: Annotated[int, Body(title="文档ID")]):

        if not (repo := await Repo.get_one(repo_id=repo_id, create_user_id=None if self.is_admin else self.user_id)):
            raise NotFoundError("未找到目标数据")
        if not (doc := await Doc.get_one(repo_id=repo_id, doc_id=doc_id)):
            raise NotFoundError("未找到目标数据")

        await Doc.delete(repo_id=repo_id, doc_id=doc_id)
        await g.session.commit()

        return self.response(message="删除成功")


class DocUploadView(BaseView):
    @api_description(summary="上传文档")
    async def post(self,
                   repo_id: Annotated[int, Form(title="文档库ID", description="文档库中上传时使用")] = 0,
                   files: list[UploadFile] = File(title="文件列表", default=None)):
        if self.is_admin:
            user_id = None
            create_user_id = None
        else:
            user_id = create_user_id = self.user_id

        repo = await Repo.get_one(repo_id=repo_id, user_id=user_id, create_user_id=create_user_id)
        if not repo:
            raise NotFoundError("未找到目标数据")

        doc_ids = []
        for file in files:
            if file.size > PARSER_FILE_SIZE * 1024 * 1024:  # 30MB
                raise ParamsCheckError(message=f"文件{file.filename}大小超过限制: {PARSER_FILE_SIZE}MB")
            suffix = Path(file.filename).suffix.lower()
            if suffix not in SupportSuffix.__members__.values():
                raise ParamsCheckError(message=f"不支持文件类型: {suffix}")

            name = file.filename
            size = file.size
            file.file.seek(0)
            content = await file.read()
            md5 = hashlib.sha256(content).hexdigest()

            doc_id = await Doc.create(repo_id=repo_id, name=name, md5=md5, size=size, status=DocStatus.uploading)
            path = Doc.get_path(repo_id=repo_id, doc_id=doc_id, filename=f"{Path(file.filename).stem}{suffix.lower()}")
            await Doc.upload_s3(path=path, content=content)
            await Doc.update(doc_id=doc_id, path=path, status=DocStatus.upload_success)

            doc_ids.append(doc_id)

        await g.session.commit()

        for doc_id in doc_ids:
            await Doc.send_doc_parsing_task(doc_id=doc_id)

        return self.response(data={"doc_ids": doc_ids})


class DocParsingRetryView(BaseView):
    @api_description(summary="解析文档")
    async def post(self,
                   doc_id: Annotated[int, Body(embed=True), Field(title="文档ID")]):
        if not (doc := await Doc.get_one(doc_id=doc_id)):
            raise NotFoundError("未找到目标数据")

        repo_id = doc["repo_id"]
        if self.is_admin:
            user_id = None
            create_user_id = None
        else:
            user_id = create_user_id = self.user_id

        repo = await Repo.get_one(repo_id=repo_id, user_id=user_id, create_user_id=create_user_id)
        if not repo:
            raise NotFoundError("未找到目标数据")

        await Doc.update(doc_id=doc_id, status=DocStatus.parsing)
        await g.session.commit()
        await Doc.send_doc_parsing_task(doc_id=doc_id)

        return self.response(message="已开始重试解析")
