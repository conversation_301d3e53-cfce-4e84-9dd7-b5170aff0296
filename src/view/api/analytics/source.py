#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Query
from pydantic import Field

from view import BaseView, api_description
from controller.analytics import Dashboard, Metrics
from exception.custom_exception import NotFoundError


class AnalyticsSourceView(BaseView):
    @api_description(summary="获取分析数据来源")
    async def get(self,
                  task_id: Annotated[int, Query(), Field(title="分析任务ID")]   ,
                  item_id: Annotated[int, Query(), Field(title="数据项ID")]):
        item = await Dashboard.get_item_one(item_id=item_id)
        if not item:
            raise NotFoundError("未找到指定数据项")
        source = await Metrics.get_source(task_id=task_id, item_id=item_id)
        if not source:
            raise NotFoundError("未找到指定任务结果")

        return self.response(data={"item_name": item["name"], **source})
