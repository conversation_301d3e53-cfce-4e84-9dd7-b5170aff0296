#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from itertools import chain
from typing import Annotated

from fastapi import Query, Body
from pydantic import Field

from engine.rdb import g
from view import BaseView, api_description
from controller.repository import Repo
from controller.analytics import Summarizer, Execution, DataRangePrefix
from controller.system_config import ModelInfo, ModelType
from exception.custom_exception import ParamsCheckError, NotFoundError


class SummarizerListView(BaseView):
    @api_description(summary="摘要分析列表")
    async def post(self,
                   repo_id: Annotated[int, Body(), Field(title="知识库ID")] = None,
                   match: Annotated[str, Body(), Field(title="模糊匹配")] = None,
                   tags: Annotated[list[str], Body(), Field(title="筛选标签")] = None,
                   page: Annotated[int, Body(), Field(title="分页页数")] = 1,
                   per_page: Annotated[int, Body(), Field(title="分页容量")] = 20,
                   order_by: Annotated[str, Body(), Field(title="排序字段")] = "create_time:desc"):
        pager, summarizers = await Summarizer.get_list(
            repo_id=repo_id, match=match, tags=tags, page=page, per_page=per_page, order_by=order_by)

        repo_ids = list(dict.fromkeys(chain.from_iterable([smz["repo_ids"] for smz in summarizers])))
        repo_id_name_mapping = await Repo.get_id_name_mapping(repo_ids)

        llm_model_id_mapping = await ModelInfo.get_id_mapping(model_type=ModelType.llm)

        for smz in summarizers:
            smz_repo_ids = smz.pop("repo_ids")
            smz["repo"] = [{
                "repo_id": repo_id,
                "repo_name": repo_id_name_mapping[repo_id]}
                for repo_id in smz_repo_ids]
            if smz["llm_models"]:
                smz["llm_models"] = [llm_model_id_mapping[llm_id] for llm_id in smz["llm_models"]]

        return self.response(data=summarizers, pager=pager)


class SummarizerAllView(BaseView):
    @api_description(summary="摘要分析列表")
    async def get(self,
                  repo_id: Annotated[int, Query(), Field(title="知识库ID")] = None,
                  match: Annotated[str, Query(), Field(title="模糊匹配")] = None,
                  order_by: Annotated[str, Query(), Field(title="排序字段")] = "create_time:desc"):
        pager, summarizers = await Summarizer.get_all(repo_id=repo_id, match=match, order_by=order_by)
        repo_ids = list(dict.fromkeys(chain.from_iterable([smz["repo_ids"] for smz in summarizers])))
        repo_id_name_mapping = await Repo.get_id_name_mapping(repo_ids)

        llm_model_id_mapping = await ModelInfo.get_id_mapping(model_type=ModelType.llm)

        for smz in summarizers:
            smz_repo_ids = smz.pop("repo_ids")
            smz["repo"] = [{
                "repo_id": repo_id,
                "repo_name": repo_id_name_mapping[repo_id]}
                for repo_id in smz_repo_ids]
            if smz["llm_models"]:
                smz["llm_models"] = [llm_model_id_mapping[llm_id] for llm_id in smz["llm_models"]]

        return self.response(data=summarizers, pager=pager)


class SummarizerTagsView(BaseView):
    @api_description(summary="摘要分析列表")
    async def get(self):
        summarizers = await Summarizer.get_tags_count()

        return self.response(data=summarizers)


class SummarizerView(BaseView):
    @api_description(summary="查询摘要分析")
    async def get(self,
                  summarizer_id: Annotated[int, Query(), Field(title="摘要分析ID")]):
        summarizer = await Summarizer.get_one(summarizer_id=summarizer_id)
        if not summarizer:
            raise NotFoundError("未找到目标数据")

        repo_id_name_mapping = await Repo.get_id_name_mapping(repo_ids=summarizer["repo_ids"])
        smz_repo_ids = summarizer.pop("repo_ids")

        llm_model_id_mapping = await ModelInfo.get_id_mapping(model_type=ModelType.llm)

        summarizer["repo"] = [{
            "repo_id": repo_id,
            "repo_name": repo_id_name_mapping[repo_id]}
            for repo_id in smz_repo_ids]
        if summarizer["llm_models"]:
            summarizer["llm_models"] = [llm_model_id_mapping[llm_id] for llm_id in summarizer["llm_models"]]

        return self.response(data=summarizer)

    @api_description(summary="创建摘要分析")
    async def post(self,
                   name: Annotated[str, Body(), Field(title="摘要分析名称", min_length=1, max_length=30)],
                   repo_ids: Annotated[list[int], Body(), Field(title="知识库IDs", min_length=1)],
                   data_range: Annotated[str, Body(), Field(title="数据范围")],
                   prompt: Annotated[str, Body(), Field(title="摘要方向", min_length=1, max_length=256)],
                   execution: Annotated[str, Body(), Field(title="执行时间", min_length=1)],
                   llm_models: Annotated[list[int], Body(), Field(title="使用模型", min_length=1)] = None,
                   tags: Annotated[list[str], Body(), Field(title="标签")] = None):
        if data_range.split(":")[0] not in DataRangePrefix.__members__.values():
            raise ParamsCheckError(message=f"不合法的数据范围: {data_range}")
        
        tags = Summarizer.cleat_tags(tags=tags)
        summarizer_id = await Summarizer.create(
            name=name, repo_ids=repo_ids, llm_models=llm_models, data_range=data_range, prompt=prompt, execution=execution,
            tags=tags)
        await g.session.commit()

        if execution == Execution.once:
            await Summarizer.send_once_task(summarizer_id=summarizer_id)
        else:
            await Summarizer.send_crontab_task(summarizer_id=summarizer_id, crontab=execution)

        return self.response(data={"summarizer_id": summarizer_id})

    @api_description(summary="修改摘要分析")
    async def put(self,
                  summarizer_id: Annotated[int, Body(), Field(title="摘要分析ID")],
                  name: Annotated[str, Body(), Field(title="摘要分析名称", min_length=1, max_length=50)] = None,
                  repo_ids: Annotated[list[int], Body(), Field(title="知识库IDs", min_length=1)] = None,
                  data_range: Annotated[str, Body(), Field(title="数据范围", min_length=1)] = None,
                  llm_models: Annotated[list[int], Body(), Field(title="使用模型", min_length=1)] = None,
                  prompt: Annotated[str, Body(), Field(title="摘要方向", min_length=1, max_length=256)] = None,
                  execution: Annotated[str, Body(), Field(title="执行时间", min_length=1)] = None,
                  tags: Annotated[list[str], Body(), Field(title="标签")] = None):
        if data_range.split(":")[0] not in DataRangePrefix.__members__.values():
            raise ParamsCheckError(message=f"不合法的数据范围: {data_range}")

        tags = Summarizer.cleat_tags(tags=tags)
        await Summarizer.update(
            summarizer_id=summarizer_id, name=name, repo_ids=repo_ids, llm_models=llm_models, data_range=data_range,
            prompt=prompt, execution=execution, tags=tags)
        await g.session.commit()
        
        await Summarizer.delete_crontab_task(summarizer_id=summarizer_id)

        if execution == Execution.once:
            await Summarizer.send_once_task(summarizer_id=summarizer_id)
        else:
            await Summarizer.send_crontab_task(summarizer_id=summarizer_id, crontab=execution)

        return self.response(message="修改成功")

    @api_description(summary="删除摘要分析")
    async def delete(self,
                     summarizer_id: Annotated[int, Body(embed=True), Field(title="摘要分析ID")]):
        await Summarizer.delete(summarizer_id=summarizer_id)
        await g.session.commit()

        await Summarizer.delete_crontab_task(summarizer_id=summarizer_id)

        return self.response(message="删除成功")


class SummarizerBulkView(BaseView):
    @api_description(summary="批量创建摘要分析")
    async def post(self,
                   data_ranges: Annotated[list[str], Body(), Field(title="数据范围", min_length=1)],
                   repo_ids: Annotated[list[int], Body(), Field(title="知识库IDs", min_length=1)],
                   llm_models: Annotated[list[str], Body(), Field(title="使用模型", min_length=1)],
                   prompt: Annotated[str, Body(), Field(title="摘要方向", min_length=1, max_length=256)],
                   execution: Annotated[str, Body(), Field(title="执行时间", min_length=1)],
                   tags: Annotated[list[str], Body(), Field(title="标签", min_length=1)] = None):
        bulk_data_range = Summarizer.clean_data_ranges(data_ranges=data_ranges)
        tags = Summarizer.cleat_tags(tags=tags)
        await Summarizer.create_bulk(
            data_ranges=bulk_data_range, repo_ids=repo_ids, llm_models=llm_models, prompt=prompt, execution=execution,
            tags=tags)
        await g.session.commit()

        return self.response(data={"rows": len(bulk_data_range)})
