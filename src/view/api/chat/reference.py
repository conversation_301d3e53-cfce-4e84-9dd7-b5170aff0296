#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Body
from pydantic import Field

from view import BaseView, api_description
from controller.repository import Repo, Doc


class QAReferenceView(BaseView):
    @api_description(summary="文档溯源")
    async def post(self,
                   citation_ids: Annotated[list[str], Body(embed=True), Field(title="角标IDs")]):
        chunks = await Doc.get_es_chunks(citation_ids=citation_ids)
        # 确认权限
        if self.is_admin:
            user_id = None
            create_user_id = None
        else:
            user_id = create_user_id = self.user_id

        repo_ids = list(dict.fromkeys([chunk["repo_id"] for chunk in chunks]))
        repos = await Repo.get_all(repo_ids=repo_ids, user_id=user_id, create_user_id=create_user_id)
        have_permission_repo_ids = [repo["repo_id"] for repo in repos]
        have_permission_chunks = [chunk for chunk in chunks if chunk["repo_id"] in have_permission_repo_ids]

        return self.response(data={"chunks": have_permission_chunks})
