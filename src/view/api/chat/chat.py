from typing import Annotated

from fastapi import Query, Body

from controller.chat.chat import <PERSON><PERSON><PERSON><PERSON><PERSON>
from controller.chat.session import Session, ChatSessionType
from view import BaseView, api_description


class ChatSessionView(BaseView):
    @api_description(summary="查询会话")
    async def get(self,
                  session_id: Annotated[str, Query(title="Session ID")]):
        session_item = await Session.get_es_one(session_id=session_id)
        return self.response(data=session_item.model_dump())

    @api_description(summary="创建会话")
    async def post(self):
        session_item = await Session.create(ChatSessionType.CHAT)
        return self.response(data=session_item.model_dump())


class ChatView(BaseView):
    @api_description(summary="开始自由问答")
    async def post(self,
                   user: Annotated[str, Body(title="用户输入的问题")],
                   session_id: Annotated[str, Body(title="会话ID")] = None):
        if session_id is None:
            session_item = await Session.create(ChatSessionType.CHAT)
            session_id = session_item.session_id

        chat_helper = ChatHelper(session_id=session_id, user=user)
        return self.stream(chat_helper.generator())
