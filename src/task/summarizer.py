#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime

from config.models import LLMModel
from common.logger import logger
from engine.rdb import g
from controller.analytics import Summarizer, SummarizerTask, SummarizerTaskState, Execution, DataRangePrefix
from controller.repository import Doc
from controller.operator.workflow.abstract import AbstractWorkflow, DocModel, ExtractModel
from controller.stats import TokenCounts, LLMBusiness
from exception.custom_exception import NotFoundError


class SummarizerOfflineTask:
    @staticmethod
    async def abstract(summarizer_id: int):
        logger.info(f"Summarizer[{summarizer_id}]: 任务开始")
        task_start_time = datetime.datetime.now()

        summarizer = await Summarizer.get_one(summarizer_id=summarizer_id)
        if not summarizer:
            raise NotFoundError("未找到摘要分析")
        # 定时任务可能会有些许偏差,将其时间从秒格式化
        if summarizer["execution"] != Execution.once:
            task_start_time = task_start_time.replace(second=0, microsecond=0)

        dr, clause = summarizer["data_range"].split(":")
        data_range_fileter = {}
        if DataRangePrefix(dr) == DataRangePrefix.keyword:
            data_range_fileter["match"] = clause.split(",")
        if DataRangePrefix(dr) == DataRangePrefix.latest:
            data_range_fileter["size"] = int(clause)
        if DataRangePrefix(dr) == DataRangePrefix.lastday:
            end_time = task_start_time
            start_time = end_time - datetime.timedelta(days=int(clause))

            data_range_fileter["start_time"] = start_time
            data_range_fileter["end_time"] = task_start_time

        docs = await Doc.get_es_all(
            repo_ids=summarizer["repo_ids"],
            order_by="data_time:asc",
            includes=["title", "html", "data_time", "doc_id",
                     "extract_result.subject",
                     "extract_result.industry",
                     "extract_result.positive_view",
                     "extract_result.negative_view",
                     "extract_result.sentiment_score",
                     "extract_result.keywords",
                     "extract_result.abstract"],
            **data_range_fileter)
        related_doc_ids = [doc["doc_id"] for doc in docs]
        summarizer_task_id = await SummarizerTask.create(
            summarizer_id=summarizer_id, summarizer_name=summarizer["name"], repo_ids=summarizer["repo_ids"],
            summarizer_config=summarizer, related_doc_ids=related_doc_ids, create_time=task_start_time)
        await g.session.commit()

        if not related_doc_ids:
            # 无文档视为任务失败
            await SummarizerTask.update(
                summarizer_task_id=summarizer_task_id, state=SummarizerTaskState.failed, message="摘要任务未找到相关数据")
            await g.session.commit()

        doc_items = [DocModel(
            title=doc["title"],
            content=doc["html"],
            data_time=doc["data_time"][:10],
            extract_result=ExtractModel(**doc["extract_result"]) if doc.get("extract_result") else None)
            for doc in docs]

        logger.info(f"Summarizer[{summarizer_id}]: 关联数据量[{len(docs)}] 开始执行AbstractRunner")
        result_doc_id = str(summarizer_task_id)
        runner = AbstractWorkflow(
            doc_list=doc_items,
            user_prompt=summarizer["prompt"]
        )
        try:
            await runner.run()
        except Exception as err:
            logger.exception(err)
            await SummarizerTask.update(
                summarizer_task_id=summarizer_task_id, state=SummarizerTaskState.failed, message=f"任务失败: {str(err)}")

            logger.error(f"Summarizer[{summarizer_id}]: 任务失败")
        else:
            await SummarizerTask.create_es(
                _id=result_doc_id, summarizer_task_id=summarizer_task_id, summarizer_id=summarizer_id,
                related_doc_ids=related_doc_ids, model=summarizer["models"][0], markdown=runner.response,
                input_tokens=sum(runner.token_consumption.input_token),
                output_tokens=sum(runner.token_consumption.output_token))
            await SummarizerTask.update(
                summarizer_task_id=summarizer_task_id, state=SummarizerTaskState.succeeded, message=f"任务成功")
            await g.session.commit()
            logger.info(f"Summarizer[{summarizer_id}]: 任务成功")
        finally:
            if runner.token_consumption:
                await TokenCounts.create(
                    model_name=LLMModel.DEEPSEEK_REASONER,
                    business=LLMBusiness.abstract,
                    input_tokens=sum(runner.token_consumption.input_token),
                    output_tokens=sum(runner.token_consumption.output_token),
                    create_user_id=summarizer["create_user_id"])
                logger.info(f"Summarizer[{summarizer_id}]: Total tokens: {sum(runner.token_consumption.input_token) + sum(runner.token_consumption.output_token)}")
            await g.session.commit()


SummarizerOffline = SummarizerOfflineTask()
