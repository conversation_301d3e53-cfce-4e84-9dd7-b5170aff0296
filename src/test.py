import json
import chardet

import httpx
from tavily import AsyncTavilyClient

from config import TVLY_APIKEY
from controller.parser.chunker import HtmlChunker, BGE_M3_TOKENIZER



client = AsyncTavilyClient(TVLY_APIKEY)
async def web_search():
    response = await client.search(
        query="浦银理财是做什么的？",
        max_results=10
    )
    print(json.dumps(response, ensure_ascii=False, indent=4))


async def web_extract():
    response = await client.extract(
        urls=["https://www.spdb-wm.com/"]
    )
    print(json.dumps(response, ensure_ascii=False, indent=4))


async def web_extract_by_url():
    async with httpx.AsyncClient(transport=httpx.AsyncHTTPTransport(retries=3)) as client:
        response = await client.get("https://www.spdb-wm.com/", follow_redirects=True)
        # 使用 response.text 自动处理编码，或者手动检测编码
        if response.encoding is None:
            # 如果没有检测到编码，尝试从内容中检测
            detected = chardet.detect(response.content)
            response.encoding = detected['encoding'] or 'utf-8'
        return response.text



if __name__ == '__main__':
    import asyncio
    import time
    s = time.time()
    html = asyncio.run(web_search())

    chunker = HtmlChunker(tokenizer=BGE_M3_TOKENIZER)
    chunks = chunker.chunk(html)
    print(json.dumps(chunks, indent=4, ensure_ascii=False))

