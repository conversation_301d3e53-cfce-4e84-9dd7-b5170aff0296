#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from engine.rdb import g, session_maker
from controller.operator.workflow.doc_extract import DocExtractWorkflow, DocModel as LLMDocModel
from controller.repository.doc import Doc, get_index


async def extract_doc(doc_id: int):
    doc = await Doc.get_one(doc_id=doc_id)
    source = await Doc.get_es_one(repo_id=doc["repo_id"], doc_id=doc["doc_id"], includes=["title", "plain_text", "data_time"])

    llm_doc = LLMDocModel(title=source["title"], content=source["plain_text"], data_time=source["data_time"][:10])
    res = await DocExtractWorkflow(doc=llm_doc).run()
    print(res)


if __name__ == '__main__':
    import asyncio

    # subject: 这个主体我建议是国家、公司、产品型号等等类型的物理主体，而不是指标等虚拟的概念主体
    # industry: 感觉上效果变差了许多,在多次抽取中,变化也比较大，是否还是一个体系下的抽取结果，以及是否考虑要求合并下;
    # 之前的结果
    """
    'subject' = {list: 2} ['财新中国制造业PMI', '制造业就业指数']
    'industry' = {list: 4} ['工业', '资本品', '机械', '工业机械']
    'positive_view' = {list: 2} ['3月财新中国制造业PMI录得51.2，较上月升高0.4个百分点，显示制造业景气度加速扩张。', '3月就业指数升至略高于荣枯线水平，为2023年9月来首次扩张，制造业就业有所改善。']
    'negative_view' = {list: 1} ['分项数据涨跌互现（部分分项可能表现不佳）。']
    'sentiment_score' = {float} 60.0
    'keywords' = {list: 2} ['3月财新中国制造业PMI: 51.2（较上月升高0.4个百分点）', '3月就业指数: 升至略高于荣枯线（2023年9月来首次扩张）']
    'abstract' = {str} '3月财新中国制造业PMI升至51.2，较上月升高0.4个百分点，显示制造业景气度加速扩张。其中，就业指数升至略高于荣枯线水平，为2023年9月以来首次扩张，表明制造业就业有所改善，但分项数据涨跌互现。'
    """
    g.session = session_maker()
    asyncio.run(extract_doc(169))