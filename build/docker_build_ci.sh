LOCAL_HARBOR_URL=harbor.iclass.bchampion.cn
IMAGE_NAME=${LOCAL_HARBOR_URL}/askask/askask-algorithm-api

cd ..

VERSION_NUMBER=1.0.0
VERSION_NAME=${CI_COMMIT_REF_NAME}
DATE=$(date "+%Y%m%d")
COMMIT_ID=$(git rev-parse --verify HEAD)
TAG=${VERSION_NAME}_v${VERSION_NUMBER}_${DATE}_${COMMIT_ID: 0: 7}

docker build -t ${IMAGE_NAME}:"${TAG}" -f build/Dockerfile .
docker push ${IMAGE_NAME}:"${TAG}"

echo ${IMAGE_NAME}:"${TAG}"
