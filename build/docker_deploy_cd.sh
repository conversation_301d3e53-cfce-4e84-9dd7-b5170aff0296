# shellcheck disable=SC2164
LOCAL_HARBOR_URL=harbor.iclass.bchampion.cn
IMAGE_NAME=${LOCAL_HARBOR_URL}/askask/askask-algorithm-api

VERSION_NUMBER=1.0.0
VERSION_NAME=${CI_COMMIT_REF_NAME}
DATE=$(date "+%Y%m%d")
COMMIT_ID=$(git rev-parse --verify HEAD)
TAG=${VERSION_NAME}_v${VERSION_NUMBER}_${DATE}_${COMMIT_ID: 0: 7}
NEW_IMAGE=${IMAGE_NAME}:${TAG}

COMMIT_USERNAME=$(git log -1 --pretty=%an)

LAST_COMMIT_MESSAGE=$(git log -1 --pretty=%B)
if [[ $LAST_COMMIT_MESSAGE == Merge* ]]; then
  LAST_COMMIT_MESSAGE=$(git log -1 --pretty=%B --skip=1)
fi

cd "${DEVELOP_DEPLOY_PATH}"
git config --global --add safe.directory "${DEVELOP_DEPLOY_PATH}"

# 打印 commit 注释
echo "Last commit message: $LAST_COMMIT_MESSAGE"

# 检查 commit 注释是否以 "deploy" 开头
if [[ $LAST_COMMIT_MESSAGE == deploy* ]]; then
  echo "Deploy message is valid."
  # 更新镜像标签
  sed -i "/algorithm-api:/ {n;s|.*|    image: ${NEW_IMAGE}|;}" docker-compose.yaml

  git add docker-compose.yaml
  git commit -m "CI/CD: ${COMMIT_ID: 0: 7} User: ${COMMIT_USERNAME} Message: ${LAST_COMMIT_MESSAGE}"
  git push origin "${VERSION_NAME}"
  # 重新启动服务
  # docker-compose restart frpc
  # docker-compose -f docker-compose.yaml up -d
fi
