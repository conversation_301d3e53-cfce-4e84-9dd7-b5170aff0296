#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import asyncio
import hashlib

from engine.es import es
from engine.rdb import load_session_context
from controller.repository import Doc, DocStatus, Repo
from controller.parser import TextDoc<PERSON><PERSON>er, TextDoc
from controller.parser.chunker import Ht<PERSON><PERSON><PERSON><PERSON><PERSON>, BGE_M3_TOKENIZER
from controller.engine import EmbeddingEngine, EmbeddingModel


@load_session_context
async def data_upgrade():
    """
    使用 search_after 分页获取从 2025-04-01 开始的数据，每次获取 10000 条
    """

    total_docs = 0
    success_docs = 0
    batch_num = 0
    search_after = None
    while True:
        batch_num += 1
        print(f"开始获取第 {batch_num} 批数据...")

        # 执行查询
        result = await es.search(
            index="doc",
            query={
                "bool": {
                    "filter": [
                        {
                            "range": {
                                "data_time": {
                                    "gte": "2025-04-01 00:00:00",
                                    "format": "yyyy-MM-dd HH:mm:ss"
                                }
                            }
                        }
                    ],
                    "should": [
                        # 条件1：tags字段包含指定股票代码
                        {
                            "terms": {
                                "tags": ["300750", "601012"]
                            }
                        },
                        # 条件2：html或filename字段包含指定公司名称
                        {
                            "bool": {
                                "should": [
                                    {
                                        "multi_match": {
                                            "query": "宁德时代",
                                            "fields": ["plain_text", "filename"],
                                            "type": "phrase"
                                        }
                                    },
                                    {
                                        "multi_match": {
                                            "query": "隆基绿能",
                                            "fields": ["plain_text", "filename"],
                                            "type": "phrase"
                                        }
                                    }
                                ]
                            }
                        }
                    ],
                    "minimum_should_match": 1  # 至少匹配一个should条件
                }
            },
            sort=[
                {"data_time": "asc"},  # 主排序字段
                {"create_time": "asc"}  # 确保排序的唯一性
            ],
            size=10000,
            search_after=search_after,
            ignore_unavailable=True
        )
        
        hits = result["hits"]["hits"]
        current_batch_size = len(hits)
        total_docs += current_batch_size
        
        print(f"获取到 {current_batch_size} 条数据")
        
        # 如果没有更多数据，退出循环
        if current_batch_size == 0:
            break
        
        # 处理本批次数据
        success_docs += await process_batch(hits)
        
        # 为下一次查询更新 search_after 参数
        # 获取最后一个文档的排序值
        if current_batch_size > 0:
            search_after = hits[-1]["sort"]
        else:
            break
    
    print(f"数据处理完成，成功 {success_docs}/{total_docs} 条文档")


@load_session_context
async def process_batch(hits: list):
    """
    处理一批文档数据
    这里是示例处理逻辑，您可以根据实际需求修改
    """
    batch_success = 0
    for doc in hits:
        source = doc["_source"]
        try:
            md5 = hashlib.sha256(source["content"].encode("utf-8")).hexdigest()
            doc_id = await Doc.create(
                repo_id=-source["database_id"], name=source["title"], size=0, md5=md5, status=DocStatus.parsing,
                create_time=source["data_time"], repetition_strategy="return")
            text_doc = TextDoc(
                doc_id=doc_id,
                data_time=source["data_time"],
                repo_id=-source["database_id"],
                title=source["title"],
                content=source["content"],
                source=source.get("source"),
                url=source.get("url"),
                author=source.get("author"),
                tags=source.get("tags", []),
                md5=md5
            )
            print(f"处理文档: ID={doc_id}, 时间={source['data_time']}, 标题={source['title'][:30]}...")
            parser = TextDocParser(
                doc_id=doc_id,
                text_doc=text_doc,
                chunker=HtmlChunker(max_tokens=1024, tokenizer=BGE_M3_TOKENIZER),
                embedding_engine=EmbeddingEngine(model=EmbeddingModel.BGE_M3),
                embedding_batch_size=8)
            await parser.exec()
        except Exception as e:
            print(e)
        else:
            batch_success += 1

    return batch_success


# 执行主函数
if __name__ == "__main__":
    asyncio.run(data_upgrade())